import React from 'react';
import { Text as RNText, TextStyle } from 'react-native';
import { useTheme } from '@/store/themeStore';
import { Theme } from '@/types/theme';

interface TextProps {
  children: React.ReactNode;
  variant?: keyof Theme['typography'];
  color?: keyof Theme['colors'];
  style?: TextStyle;
  numberOfLines?: number;
  onPress?: () => void;
}

export const Text: React.FC<TextProps> = ({
  children,
  variant = 'body',
  color = 'textPrimary',
  style,
  numberOfLines,
  onPress,
}) => {
  const theme = useTheme();

  const getTextStyle = (): TextStyle => {
    const typography = theme.typography[variant];
    return {
      fontFamily: theme.typography.fontFamily,
      fontSize: typography.fontSize,
      fontWeight: typography.fontWeight,
      lineHeight: typography.lineHeight,
      color: theme.colors[color],
    };
  };

  return (
    <RNText
      style={[getTextStyle(), style]}
      numberOfLines={numberOfLines}
      onPress={onPress}
    >
      {children}
    </RNText>
  );
};
