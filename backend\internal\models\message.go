package models

import (
	"time"

	"gorm.io/gorm"
)

// MessageRole represents the role of the message sender
type MessageRole string

const (
	MessageRoleUser      MessageRole = "user"
	MessageRoleAssistant MessageRole = "assistant"
	MessageRoleSystem    MessageRole = "system"
)

// Message represents a message in a conversation
type Message struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Message fields
	Content string      `json:"content" gorm:"type:text;not null" validate:"required"`
	Role    MessageRole `json:"role" gorm:"not null" validate:"required,oneof=user assistant system"`
	Tokens  int         `json:"tokens" gorm:"default:0"`

	// Foreign keys
	ConversationID uint `json:"conversation_id" gorm:"not null"`

	// Relationships
	Conversation Conversation `json:"conversation,omitempty" gorm:"foreignKey:ConversationID"`
}

// MessageCreateRequest represents the request payload for creating a message
type MessageCreateRequest struct {
	Content string      `json:"content" validate:"required"`
	Role    MessageRole `json:"role" validate:"required,oneof=user assistant system"`
}

// MessageResponse represents the response payload for message data
type MessageResponse struct {
	ID             uint        `json:"id"`
	CreatedAt      time.Time   `json:"created_at"`
	UpdatedAt      time.Time   `json:"updated_at"`
	Content        string      `json:"content"`
	Role           MessageRole `json:"role"`
	Tokens         int         `json:"tokens"`
	ConversationID uint        `json:"conversation_id"`
}

// ToResponse converts a Message model to MessageResponse
func (m *Message) ToResponse() MessageResponse {
	return MessageResponse{
		ID:             m.ID,
		CreatedAt:      m.CreatedAt,
		UpdatedAt:      m.UpdatedAt,
		Content:        m.Content,
		Role:           m.Role,
		Tokens:         m.Tokens,
		ConversationID: m.ConversationID,
	}
}
