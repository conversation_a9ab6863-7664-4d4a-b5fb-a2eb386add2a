// Theme Types based on the design document
export interface Colors {
  // Primary Colors
  primary: string;           // #0F766E (Dark Teal)
  primaryLight: string;      // #CCFBF1 (Light Teal/Mint)
  accent: string;            // #F97316 (Vibrant Orange)
  
  // Text Colors
  textPrimary: string;       // #1F2937 (Almost Black)
  textSecondary: string;     // #6B7281 (Medium Gray)
  textOnDark: string;        // #FFFFFF (White)
  
  // Background Colors
  backgroundScreen: string;  // #F8FAFC (Very Light Gray)
  backgroundCard: string;    // #FFFFFF (White)
  
  // Border Colors
  border: string;            // #E5E7EB (Light Gray)
  
  // Status Colors
  success: string;
  warning: string;
  error: string;
  info: string;
}

export interface Typography {
  fontFamily: string;
  
  // Font Sizes
  heading1: {
    fontSize: number;        // 24px
    fontWeight: string;      // 600 (Semi-Bold)
    lineHeight: number;
  };
  
  largeDisplay: {
    fontSize: number;        // 32px
    fontWeight: string;      // 500 (Medium)
    lineHeight: number;
  };
  
  cardTitle: {
    fontSize: number;        // 16px
    fontWeight: string;      // 600 (Semi-Bold)
    lineHeight: number;
  };
  
  body: {
    fontSize: number;        // 14px
    fontWeight: string;      // 400 (Regular)
    lineHeight: number;
  };
  
  small: {
    fontSize: number;        // 12px
    fontWeight: string;      // 400 (Regular)
    lineHeight: number;
  };
  
  button: {
    fontSize: number;        // 14px
    fontWeight: string;      // 600 (Semi-Bold)
    lineHeight: number;
  };
}

export interface Spacing {
  xs: number;    // 4px
  sm: number;    // 8px
  md: number;    // 12px
  lg: number;    // 16px
  xl: number;    // 20px
  xxl: number;   // 24px
  xxxl: number;  // 32px
}

export interface BorderRadius {
  small: number;   // 8px
  medium: number;  // 12px
  large: number;   // 16px
  pill: number;    // 20px (pill-shaped)
}

export interface Shadows {
  card: {
    shadowColor: string;
    shadowOffset: {
      width: number;
      height: number;
    };
    shadowOpacity: number;
    shadowRadius: number;
    elevation: number; // for Android
  };
}

export interface Theme {
  colors: Colors;
  typography: Typography;
  spacing: Spacing;
  borderRadius: BorderRadius;
  shadows: Shadows;
}

// Component Variants
export type ButtonVariant = 'solid' | 'outline' | 'ghost';
export type ButtonSize = 'small' | 'medium' | 'large';
export type CardVariant = 'default' | 'status' | 'summary';
export type StatusType = 'scheduled' | 'in-progress' | 'completed' | 'missed';
