package middleware

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"go.uber.org/zap"
)

// ValidationError represents a validation error response
type ValidationError struct {
	Field   string `json:"field"`
	Message string `json:"message"`
}

// ErrorResponse represents an error response
type ErrorResponse struct {
	Error   string            `json:"error"`
	Details []ValidationError `json:"details,omitempty"`
}

// ValidateJSON creates a middleware that validates JSON request bodies
func ValidateJSON(logger *zap.Logger) gin.HandlerFunc {
	validate := validator.New()

	return func(c *gin.Context) {
		// Skip validation for GET requests and requests without body
		if c.Request.Method == "GET" || c.Request.ContentLength == 0 {
			c.Next()
			return
		}

		// Check content type
		contentType := c.GetHeader("Content-Type")
		if contentType != "application/json" {
			logger.Warn("Invalid content type", zap.String("content_type", contentType))
			c.<PERSON>(http.StatusBadRequest, ErrorResponse{
				Error: "Content-Type must be application/json",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// ValidateStruct validates a struct and returns formatted errors
func ValidateStruct(validate *validator.Validate, s interface{}) []ValidationError {
	var errors []ValidationError

	err := validate.Struct(s)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			var element ValidationError
			element.Field = err.Field()
			element.Message = getErrorMessage(err)
			errors = append(errors, element)
		}
	}

	return errors
}

// getErrorMessage returns a user-friendly error message for validation errors
func getErrorMessage(fe validator.FieldError) string {
	switch fe.Tag() {
	case "required":
		return "This field is required"
	case "email":
		return "Invalid email format"
	case "min":
		return "Value is too short (minimum " + fe.Param() + " characters)"
	case "max":
		return "Value is too long (maximum " + fe.Param() + " characters)"
	case "oneof":
		return "Value must be one of: " + fe.Param()
	default:
		return "Invalid value"
	}
}

// BindAndValidate binds JSON to struct and validates it
func BindAndValidate(c *gin.Context, obj interface{}, logger *zap.Logger) bool {
	validate := validator.New()

	if err := c.ShouldBindJSON(obj); err != nil {
		logger.Warn("JSON binding failed", zap.Error(err))
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error: "Invalid JSON format",
		})
		return false
	}

	if validationErrors := ValidateStruct(validate, obj); len(validationErrors) > 0 {
		logger.Warn("Validation failed", zap.Any("errors", validationErrors))
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Validation failed",
			Details: validationErrors,
		})
		return false
	}

	return true
}
