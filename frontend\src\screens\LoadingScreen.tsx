import React from 'react';
import { View, ActivityIndicator, StyleSheet } from 'react-native';
import { useTheme } from '@/store/themeStore';
import { Text } from '@/components/atoms/Text';

export const LoadingScreen: React.FC = () => {
  const theme = useTheme();

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.backgroundScreen }]}>
      <ActivityIndicator size="large" color={theme.colors.primary} />
      <Text
        variant="body"
        color="textSecondary"
        style={{ marginTop: theme.spacing.lg }}
      >
        Loading...
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
