// API Response Types
export interface ApiResponse<T> {
  data?: T;
  error?: string;
  message?: string;
}

// User Types
export interface User {
  id: number;
  created_at: string;
  updated_at: string;
  email: string;
  username: string;
  first_name: string;
  last_name: string;
  role: string;
  is_active: boolean;
}

export interface UserCreateRequest {
  email: string;
  username: string;
  password: string;
  first_name: string;
  last_name: string;
}

export interface UserUpdateRequest {
  email?: string;
  username?: string;
  first_name?: string;
  last_name?: string;
  is_active?: boolean;
}

// Auth Types
export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  token: string;
  user: User;
}

// Agent Types
export interface Agent {
  id: number;
  created_at: string;
  updated_at: string;
  name: string;
  description: string;
  model: string;
  system_prompt: string;
  temperature: number;
  max_tokens: number;
  is_active: boolean;
  user_id: number;
}

export interface AgentCreateRequest {
  name: string;
  description: string;
  model: string;
  system_prompt: string;
  temperature: number;
  max_tokens: number;
}

export interface AgentUpdateRequest {
  name?: string;
  description?: string;
  model?: string;
  system_prompt?: string;
  temperature?: number;
  max_tokens?: number;
  is_active?: boolean;
}

// Conversation Types
export interface Conversation {
  id: number;
  created_at: string;
  updated_at: string;
  title: string;
  is_active: boolean;
  user_id: number;
  agent_id: number;
}

export interface ConversationWithMessages extends Conversation {
  messages: Message[];
}

export interface ConversationCreateRequest {
  title: string;
  agent_id: number;
}

export interface ConversationUpdateRequest {
  title?: string;
  is_active?: boolean;
}

// Message Types
export type MessageRole = 'user' | 'assistant' | 'system';

export interface Message {
  id: number;
  created_at: string;
  updated_at: string;
  content: string;
  role: MessageRole;
  tokens: number;
  conversation_id: number;
}

export interface MessageCreateRequest {
  content: string;
  role: MessageRole;
}

// Task Types
export type TaskStatus = 'pending' | 'in_progress' | 'completed' | 'failed' | 'cancelled';
export type TaskPriority = 'low' | 'medium' | 'high' | 'urgent';

export interface Task {
  id: number;
  created_at: string;
  updated_at: string;
  title: string;
  description: string;
  status: TaskStatus;
  priority: TaskPriority;
  due_date?: string;
  completed_at?: string;
  user_id: number;
  agent_id?: number;
  parent_id?: number;
}

export interface TaskCreateRequest {
  title: string;
  description: string;
  priority: TaskPriority;
  due_date?: string;
  agent_id?: number;
  parent_id?: number;
}

export interface TaskUpdateRequest {
  title?: string;
  description?: string;
  status?: TaskStatus;
  priority?: TaskPriority;
  due_date?: string;
  agent_id?: number;
}
