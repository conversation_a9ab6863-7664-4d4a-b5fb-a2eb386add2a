{"info": {"name": "AI Agent API", "description": "Complete API collection for the AI Agent application", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_postman_id": "ai-agent-api-collection"}, "variable": [{"key": "baseUrl", "value": "http://localhost:8080/api/v1", "type": "string"}, {"key": "token", "value": "", "type": "string"}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "item": [{"name": "Health", "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/health", "host": ["{{baseUrl}}"], "path": ["health"]}, "description": "Check if the API is running"}, "response": []}]}, {"name": "Authentication", "item": [{"name": "Register User", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('token', response.token);", "}"]}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"username\": \"testuser\",\n  \"password\": \"password123\",\n  \"first_name\": \"Test\",\n  \"last_name\": \"User\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/register", "host": ["{{baseUrl}}"], "path": ["auth", "register"]}, "description": "Register a new user account"}, "response": []}, {"name": "Login User", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('token', response.token);", "}"]}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/login", "host": ["{{baseUrl}}"], "path": ["auth", "login"]}, "description": "Login with email and password"}, "response": []}, {"name": "Refresh <PERSON>", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('token', response.token);", "}"]}}], "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/auth/refresh", "host": ["{{baseUrl}}"], "path": ["auth", "refresh"]}, "description": "Refresh JWT token"}, "response": []}]}, {"name": "Users", "item": [{"name": "Get Current User", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/users/me", "host": ["{{baseUrl}}"], "path": ["users", "me"]}, "description": "Get current authenticated user information"}, "response": []}, {"name": "Update Current User", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"first_name\": \"Updated\",\n  \"last_name\": \"Name\"\n}"}, "url": {"raw": "{{baseUrl}}/users/me", "host": ["{{baseUrl}}"], "path": ["users", "me"]}, "description": "Update current user information"}, "response": []}]}, {"name": "Agents", "item": [{"name": "Get Agents", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/agents", "host": ["{{baseUrl}}"], "path": ["agents"]}, "description": "Get all agents for current user"}, "response": []}, {"name": "Create Agent", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('agentId', response.id);", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Test Agent\",\n  \"description\": \"A test AI agent\",\n  \"model\": \"gpt-3.5-turbo\",\n  \"system_prompt\": \"You are a helpful assistant.\",\n  \"temperature\": 0.7,\n  \"max_tokens\": 1000\n}"}, "url": {"raw": "{{baseUrl}}/agents", "host": ["{{baseUrl}}"], "path": ["agents"]}, "description": "Create a new AI agent"}, "response": []}, {"name": "Get Agent by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/agents/{{agentId}}", "host": ["{{baseUrl}}"], "path": ["agents", "{{agentId}}"]}, "description": "Get specific agent by ID"}, "response": []}, {"name": "Update Agent", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Agent Name\",\n  \"description\": \"Updated description\"\n}"}, "url": {"raw": "{{baseUrl}}/agents/{{agentId}}", "host": ["{{baseUrl}}"], "path": ["agents", "{{agentId}}"]}, "description": "Update agent information"}, "response": []}, {"name": "Delete Agent", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/agents/{{agentId}}", "host": ["{{baseUrl}}"], "path": ["agents", "{{agentId}}"]}, "description": "Delete an agent"}, "response": []}]}, {"name": "Conversations", "item": [{"name": "Get Conversations", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/conversations", "host": ["{{baseUrl}}"], "path": ["conversations"]}, "description": "Get all conversations for current user"}, "response": []}, {"name": "Create Conversation", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('conversationId', response.id);", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Test Conversation\",\n  \"agent_id\": {{agentId}}\n}"}, "url": {"raw": "{{baseUrl}}/conversations", "host": ["{{baseUrl}}"], "path": ["conversations"]}, "description": "Create a new conversation"}, "response": []}]}, {"name": "Tasks", "item": [{"name": "Get Tasks", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/tasks", "host": ["{{baseUrl}}"], "path": ["tasks"]}, "description": "Get all tasks for current user"}, "response": []}, {"name": "Create Task", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Test Task\",\n  \"description\": \"A test task\",\n  \"priority\": \"medium\"\n}"}, "url": {"raw": "{{baseUrl}}/tasks", "host": ["{{baseUrl}}"], "path": ["tasks"]}, "description": "Create a new task"}, "response": []}]}]}