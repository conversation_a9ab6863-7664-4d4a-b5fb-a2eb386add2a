package handlers

import (
	"net/http"
	"strconv"

	"github.com/bluehorn/ai-agent/internal/middleware"
	"github.com/bluehorn/ai-agent/internal/models"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

// UserHandler handles user-related requests
type UserHandler struct {
	db     *gorm.DB
	logger *zap.Logger
}

// NewUserHandler creates a new user handler
func NewUserHandler(db *gorm.DB, logger *zap.Logger) *UserHandler {
	return &UserHandler{
		db:     db,
		logger: logger,
	}
}

// GetCurrentUser returns the current authenticated user
// @Summary Get current user
// @Description Get the current authenticated user's information
// @Tags users
// @Security BearerAuth
// @Produce json
// @Success 200 {object} models.UserResponse
// @Failure 401 {object} middleware.ErrorResponse
// @Router /users/me [get]
func (h *UserHandler) GetCurrentUser(c *gin.Context) {
	userInterface, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusUnauthorized, middleware.ErrorResponse{
			Error: "User not found in context",
		})
		return
	}

	user := userInterface.(models.User)
	c.JSON(http.StatusOK, user.ToResponse())
}

// UpdateCurrentUser updates the current authenticated user
// @Summary Update current user
// @Description Update the current authenticated user's information
// @Tags users
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param user body models.UserUpdateRequest true "User update data"
// @Success 200 {object} models.UserResponse
// @Failure 400 {object} middleware.ErrorResponse
// @Failure 401 {object} middleware.ErrorResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /users/me [put]
func (h *UserHandler) UpdateCurrentUser(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, middleware.ErrorResponse{
			Error: "User ID not found in context",
		})
		return
	}

	var req models.UserUpdateRequest
	if !middleware.BindAndValidate(c, &req, h.logger) {
		return
	}

	var user models.User
	if err := h.db.First(&user, userID).Error; err != nil {
		h.logger.Error("User not found", zap.Error(err))
		c.JSON(http.StatusNotFound, middleware.ErrorResponse{
			Error: "User not found",
		})
		return
	}

	// Update fields if provided
	if req.Email != nil {
		user.Email = *req.Email
	}
	if req.Username != nil {
		user.Username = *req.Username
	}
	if req.FirstName != nil {
		user.FirstName = *req.FirstName
	}
	if req.LastName != nil {
		user.LastName = *req.LastName
	}
	if req.IsActive != nil {
		user.IsActive = *req.IsActive
	}

	if err := h.db.Save(&user).Error; err != nil {
		h.logger.Error("Failed to update user", zap.Error(err))
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error: "Failed to update user",
		})
		return
	}

	h.logger.Info("User updated successfully", zap.Uint("user_id", user.ID))
	c.JSON(http.StatusOK, user.ToResponse())
}

// DeleteCurrentUser deletes the current authenticated user
// @Summary Delete current user
// @Description Delete the current authenticated user's account
// @Tags users
// @Security BearerAuth
// @Produce json
// @Success 204
// @Failure 401 {object} middleware.ErrorResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /users/me [delete]
func (h *UserHandler) DeleteCurrentUser(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, middleware.ErrorResponse{
			Error: "User ID not found in context",
		})
		return
	}

	if err := h.db.Delete(&models.User{}, userID).Error; err != nil {
		h.logger.Error("Failed to delete user", zap.Error(err))
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error: "Failed to delete user",
		})
		return
	}

	h.logger.Info("User deleted successfully", zap.Any("user_id", userID))
	c.Status(http.StatusNoContent)
}

// GetUsers returns all users (admin only)
// @Summary Get all users
// @Description Get a list of all users (admin only)
// @Tags users
// @Security BearerAuth
// @Produce json
// @Success 200 {array} models.UserResponse
// @Failure 401 {object} middleware.ErrorResponse
// @Failure 403 {object} middleware.ErrorResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /users [get]
func (h *UserHandler) GetUsers(c *gin.Context) {
	var users []models.User
	if err := h.db.Find(&users).Error; err != nil {
		h.logger.Error("Failed to fetch users", zap.Error(err))
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error: "Failed to fetch users",
		})
		return
	}

	responses := make([]models.UserResponse, len(users))
	for i, user := range users {
		responses[i] = user.ToResponse()
	}

	c.JSON(http.StatusOK, responses)
}

// GetUser returns a specific user by ID (admin only)
// @Summary Get user by ID
// @Description Get a specific user by ID (admin only)
// @Tags users
// @Security BearerAuth
// @Produce json
// @Param id path int true "User ID"
// @Success 200 {object} models.UserResponse
// @Failure 400 {object} middleware.ErrorResponse
// @Failure 401 {object} middleware.ErrorResponse
// @Failure 403 {object} middleware.ErrorResponse
// @Failure 404 {object} middleware.ErrorResponse
// @Router /users/{id} [get]
func (h *UserHandler) GetUser(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, middleware.ErrorResponse{
			Error: "Invalid user ID",
		})
		return
	}

	var user models.User
	if err := h.db.First(&user, uint(id)).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, middleware.ErrorResponse{
				Error: "User not found",
			})
			return
		}
		h.logger.Error("Failed to fetch user", zap.Error(err))
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error: "Failed to fetch user",
		})
		return
	}

	c.JSON(http.StatusOK, user.ToResponse())
}

// UpdateUser updates a specific user by ID (admin only)
// @Summary Update user by ID
// @Description Update a specific user by ID (admin only)
// @Tags users
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "User ID"
// @Param user body models.UserUpdateRequest true "User update data"
// @Success 200 {object} models.UserResponse
// @Failure 400 {object} middleware.ErrorResponse
// @Failure 401 {object} middleware.ErrorResponse
// @Failure 403 {object} middleware.ErrorResponse
// @Failure 404 {object} middleware.ErrorResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /users/{id} [put]
func (h *UserHandler) UpdateUser(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, middleware.ErrorResponse{
			Error: "Invalid user ID",
		})
		return
	}

	var req models.UserUpdateRequest
	if !middleware.BindAndValidate(c, &req, h.logger) {
		return
	}

	var user models.User
	if err := h.db.First(&user, uint(id)).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, middleware.ErrorResponse{
				Error: "User not found",
			})
			return
		}
		h.logger.Error("Failed to fetch user", zap.Error(err))
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error: "Failed to fetch user",
		})
		return
	}

	// Update fields if provided
	if req.Email != nil {
		user.Email = *req.Email
	}
	if req.Username != nil {
		user.Username = *req.Username
	}
	if req.FirstName != nil {
		user.FirstName = *req.FirstName
	}
	if req.LastName != nil {
		user.LastName = *req.LastName
	}
	if req.IsActive != nil {
		user.IsActive = *req.IsActive
	}

	if err := h.db.Save(&user).Error; err != nil {
		h.logger.Error("Failed to update user", zap.Error(err))
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error: "Failed to update user",
		})
		return
	}

	h.logger.Info("User updated successfully", zap.Uint("user_id", user.ID))
	c.JSON(http.StatusOK, user.ToResponse())
}

// DeleteUser deletes a specific user by ID (admin only)
// @Summary Delete user by ID
// @Description Delete a specific user by ID (admin only)
// @Tags users
// @Security BearerAuth
// @Produce json
// @Param id path int true "User ID"
// @Success 204
// @Failure 400 {object} middleware.ErrorResponse
// @Failure 401 {object} middleware.ErrorResponse
// @Failure 403 {object} middleware.ErrorResponse
// @Failure 404 {object} middleware.ErrorResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /users/{id} [delete]
func (h *UserHandler) DeleteUser(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, middleware.ErrorResponse{
			Error: "Invalid user ID",
		})
		return
	}

	if err := h.db.Delete(&models.User{}, uint(id)).Error; err != nil {
		h.logger.Error("Failed to delete user", zap.Error(err))
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error: "Failed to delete user",
		})
		return
	}

	h.logger.Info("User deleted successfully", zap.Uint("user_id", uint(id)))
	c.Status(http.StatusNoContent)
}
