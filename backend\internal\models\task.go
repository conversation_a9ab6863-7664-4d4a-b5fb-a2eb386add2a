package models

import (
	"time"

	"gorm.io/gorm"
)

// TaskStatus represents the status of a task
type TaskStatus string

const (
	TaskStatusPending    TaskStatus = "pending"
	TaskStatusInProgress TaskStatus = "in_progress"
	TaskStatusCompleted  TaskStatus = "completed"
	TaskStatusFailed     TaskStatus = "failed"
	TaskStatusCancelled  TaskStatus = "cancelled"
)

// TaskPriority represents the priority of a task
type TaskPriority string

const (
	TaskPriorityLow    TaskPriority = "low"
	TaskPriorityMedium TaskPriority = "medium"
	TaskPriorityHigh   TaskPriority = "high"
	TaskPriorityUrgent TaskPriority = "urgent"
)

// Task represents a task in the system
type Task struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Task fields
	Title       string       `json:"title" gorm:"not null" validate:"required,min=1,max=200"`
	Description string       `json:"description" validate:"max=1000"`
	Status      TaskStatus   `json:"status" gorm:"default:pending" validate:"oneof=pending in_progress completed failed cancelled"`
	Priority    TaskPriority `json:"priority" gorm:"default:medium" validate:"oneof=low medium high urgent"`
	DueDate     *time.Time   `json:"due_date,omitempty"`
	CompletedAt *time.Time   `json:"completed_at,omitempty"`

	// Foreign keys
	UserID   uint  `json:"user_id" gorm:"not null"`
	AgentID  *uint `json:"agent_id,omitempty"`
	ParentID *uint `json:"parent_id,omitempty"`

	// Relationships
	User     User   `json:"user,omitempty" gorm:"foreignKey:UserID"`
	Agent    *Agent `json:"agent,omitempty" gorm:"foreignKey:AgentID"`
	Parent   *Task  `json:"parent,omitempty" gorm:"foreignKey:ParentID"`
	Subtasks []Task `json:"subtasks,omitempty" gorm:"foreignKey:ParentID"`
}

// TaskCreateRequest represents the request payload for creating a task
type TaskCreateRequest struct {
	Title       string       `json:"title" validate:"required,min=1,max=200"`
	Description string       `json:"description" validate:"max=1000"`
	Priority    TaskPriority `json:"priority" validate:"oneof=low medium high urgent"`
	DueDate     *time.Time   `json:"due_date,omitempty"`
	AgentID     *uint        `json:"agent_id,omitempty"`
	ParentID    *uint        `json:"parent_id,omitempty"`
}

// TaskUpdateRequest represents the request payload for updating a task
type TaskUpdateRequest struct {
	Title       *string       `json:"title,omitempty" validate:"omitempty,min=1,max=200"`
	Description *string       `json:"description,omitempty" validate:"omitempty,max=1000"`
	Status      *TaskStatus   `json:"status,omitempty" validate:"omitempty,oneof=pending in_progress completed failed cancelled"`
	Priority    *TaskPriority `json:"priority,omitempty" validate:"omitempty,oneof=low medium high urgent"`
	DueDate     *time.Time    `json:"due_date,omitempty"`
	AgentID     *uint         `json:"agent_id,omitempty"`
}

// TaskResponse represents the response payload for task data
type TaskResponse struct {
	ID          uint         `json:"id"`
	CreatedAt   time.Time    `json:"created_at"`
	UpdatedAt   time.Time    `json:"updated_at"`
	Title       string       `json:"title"`
	Description string       `json:"description"`
	Status      TaskStatus   `json:"status"`
	Priority    TaskPriority `json:"priority"`
	DueDate     *time.Time   `json:"due_date,omitempty"`
	CompletedAt *time.Time   `json:"completed_at,omitempty"`
	UserID      uint         `json:"user_id"`
	AgentID     *uint        `json:"agent_id,omitempty"`
	ParentID    *uint        `json:"parent_id,omitempty"`
}

// ToResponse converts a Task model to TaskResponse
func (t *Task) ToResponse() TaskResponse {
	return TaskResponse{
		ID:          t.ID,
		CreatedAt:   t.CreatedAt,
		UpdatedAt:   t.UpdatedAt,
		Title:       t.Title,
		Description: t.Description,
		Status:      t.Status,
		Priority:    t.Priority,
		DueDate:     t.DueDate,
		CompletedAt: t.CompletedAt,
		UserID:      t.UserID,
		AgentID:     t.AgentID,
		ParentID:    t.ParentID,
	}
}
