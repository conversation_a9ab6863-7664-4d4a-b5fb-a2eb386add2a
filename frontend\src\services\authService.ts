import apiClient, { handleApiResponse } from './api';
import { LoginRequest, LoginResponse, UserCreateRequest } from '@/types/api';

export const authService = {
  // Login user
  login: async (credentials: LoginRequest): Promise<LoginResponse> => {
    const response = await apiClient.post<LoginResponse>('/auth/login', credentials);
    return handleApiResponse(response);
  },

  // Register user
  register: async (userData: UserCreateRequest): Promise<LoginResponse> => {
    const response = await apiClient.post<LoginResponse>('/auth/register', userData);
    return handleApiResponse(response);
  },

  // Refresh token
  refreshToken: async (): Promise<LoginResponse> => {
    const response = await apiClient.post<LoginResponse>('/auth/refresh');
    return handleApiResponse(response);
  },

  // Logout (client-side only, no API call needed)
  logout: () => {
    // Clear any client-side data if needed
    return Promise.resolve();
  },
};
