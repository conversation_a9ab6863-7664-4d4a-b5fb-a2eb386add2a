package models

import (
	"time"

	"gorm.io/gorm"
)

// Agent represents an AI agent in the system
type Agent struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Agent fields
	Name        string `json:"name" gorm:"not null" validate:"required,min=1,max=100"`
	Description string `json:"description" validate:"max=500"`
	Model       string `json:"model" gorm:"not null" validate:"required"`
	SystemPrompt string `json:"system_prompt" validate:"max=2000"`
	Temperature float32 `json:"temperature" gorm:"default:0.7" validate:"min=0,max=2"`
	MaxTokens   int     `json:"max_tokens" gorm:"default:1000" validate:"min=1,max=4000"`
	IsActive    bool    `json:"is_active" gorm:"default:true"`

	// Foreign keys
	UserID uint `json:"user_id" gorm:"not null"`

	// Relationships
	User          User           `json:"user,omitempty" gorm:"foreignKey:UserID"`
	Conversations []Conversation `json:"conversations,omitempty" gorm:"foreignKey:AgentID"`
}

// AgentCreateRequest represents the request payload for creating an agent
type AgentCreateRequest struct {
	Name         string  `json:"name" validate:"required,min=1,max=100"`
	Description  string  `json:"description" validate:"max=500"`
	Model        string  `json:"model" validate:"required"`
	SystemPrompt string  `json:"system_prompt" validate:"max=2000"`
	Temperature  float32 `json:"temperature" validate:"min=0,max=2"`
	MaxTokens    int     `json:"max_tokens" validate:"min=1,max=4000"`
}

// AgentUpdateRequest represents the request payload for updating an agent
type AgentUpdateRequest struct {
	Name         *string  `json:"name,omitempty" validate:"omitempty,min=1,max=100"`
	Description  *string  `json:"description,omitempty" validate:"omitempty,max=500"`
	Model        *string  `json:"model,omitempty"`
	SystemPrompt *string  `json:"system_prompt,omitempty" validate:"omitempty,max=2000"`
	Temperature  *float32 `json:"temperature,omitempty" validate:"omitempty,min=0,max=2"`
	MaxTokens    *int     `json:"max_tokens,omitempty" validate:"omitempty,min=1,max=4000"`
	IsActive     *bool    `json:"is_active,omitempty"`
}

// AgentResponse represents the response payload for agent data
type AgentResponse struct {
	ID           uint      `json:"id"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
	Name         string    `json:"name"`
	Description  string    `json:"description"`
	Model        string    `json:"model"`
	SystemPrompt string    `json:"system_prompt"`
	Temperature  float32   `json:"temperature"`
	MaxTokens    int       `json:"max_tokens"`
	IsActive     bool      `json:"is_active"`
	UserID       uint      `json:"user_id"`
}

// ToResponse converts an Agent model to AgentResponse
func (a *Agent) ToResponse() AgentResponse {
	return AgentResponse{
		ID:           a.ID,
		CreatedAt:    a.CreatedAt,
		UpdatedAt:    a.UpdatedAt,
		Name:         a.Name,
		Description:  a.Description,
		Model:        a.Model,
		SystemPrompt: a.SystemPrompt,
		Temperature:  a.Temperature,
		MaxTokens:    a.MaxTokens,
		IsActive:     a.IsActive,
		UserID:       a.UserID,
	}
}
