import React from 'react';
import { View, StyleSheet, ScrollView, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTheme } from '@/store/themeStore';
import { useAuth, useAuthActions } from '@/store/authStore';
import { Text } from '@/components/atoms/Text';
import { Card } from '@/components/atoms/Card';
import { Button } from '@/components/atoms/Button';

export const ProfileScreen: React.FC = () => {
  const theme = useTheme();
  const { user } = useAuth();
  const { clearAuth } = useAuthActions();

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Logout', 
          style: 'destructive',
          onPress: () => clearAuth()
        },
      ]
    );
  };

  if (!user) return null;

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.backgroundScreen }]}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <Card style={styles.profileCard}>
          <View style={styles.avatarContainer}>
            <View style={[styles.avatar, { backgroundColor: theme.colors.primary }]}>
              <Text variant="heading1" color="textOnDark">
                {user.first_name.charAt(0)}{user.last_name.charAt(0)}
              </Text>
            </View>
          </View>
          
          <Text variant="cardTitle" color="textPrimary" style={styles.name}>
            {user.first_name} {user.last_name}
          </Text>
          <Text variant="body" color="textSecondary" style={styles.email}>
            {user.email}
          </Text>
          <Text variant="small" color="textSecondary" style={styles.username}>
            @{user.username}
          </Text>
        </Card>

        <Card style={styles.infoCard}>
          <Text variant="cardTitle" color="textPrimary" style={styles.sectionTitle}>
            Account Information
          </Text>
          
          <View style={styles.infoRow}>
            <Text variant="body" color="textSecondary">Role:</Text>
            <Text variant="body" color="textPrimary" style={styles.infoValue}>
              {user.role.charAt(0).toUpperCase() + user.role.slice(1)}
            </Text>
          </View>
          
          <View style={styles.infoRow}>
            <Text variant="body" color="textSecondary">Status:</Text>
            <Text 
              variant="body" 
              color={user.is_active ? "success" : "error"} 
              style={styles.infoValue}
            >
              {user.is_active ? 'Active' : 'Inactive'}
            </Text>
          </View>
          
          <View style={styles.infoRow}>
            <Text variant="body" color="textSecondary">Member since:</Text>
            <Text variant="body" color="textPrimary" style={styles.infoValue}>
              {new Date(user.created_at).toLocaleDateString()}
            </Text>
          </View>
        </Card>

        <Card style={styles.actionsCard}>
          <Text variant="cardTitle" color="textPrimary" style={styles.sectionTitle}>
            Actions
          </Text>
          
          <Button
            title="Edit Profile"
            variant="outline"
            onPress={() => {
              // TODO: Navigate to edit profile screen
              Alert.alert('Coming Soon', 'Profile editing will be available soon!');
            }}
            style={styles.actionButton}
          />
          
          <Button
            title="Settings"
            variant="outline"
            onPress={() => {
              // TODO: Navigate to settings screen
              Alert.alert('Coming Soon', 'Settings will be available soon!');
            }}
            style={styles.actionButton}
          />
          
          <Button
            title="Logout"
            variant="outline"
            onPress={handleLogout}
            style={[styles.actionButton, { borderColor: theme.colors.error }]}
            textStyle={{ color: theme.colors.error }}
          />
        </Card>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    padding: 20,
  },
  profileCard: {
    alignItems: 'center',
    marginBottom: 20,
    paddingVertical: 32,
  },
  avatarContainer: {
    marginBottom: 16,
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  name: {
    marginBottom: 4,
    textAlign: 'center',
  },
  email: {
    marginBottom: 4,
    textAlign: 'center',
  },
  username: {
    textAlign: 'center',
  },
  infoCard: {
    marginBottom: 20,
  },
  actionsCard: {
    marginBottom: 20,
  },
  sectionTitle: {
    marginBottom: 16,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  infoValue: {
    fontWeight: '500',
  },
  actionButton: {
    marginBottom: 12,
  },
});
