import { Theme } from '@/types/theme';

export const theme: Theme = {
  colors: {
    // Primary Colors
    primary: '#0F766E',           // Dark Teal
    primaryLight: '#CCFBF1',      // Light Teal/Mint
    accent: '#F97316',            // Vibrant Orange
    
    // Text Colors
    textPrimary: '#1F2937',       // Almost Black
    textSecondary: '#6B7281',     // Medium Gray
    textOnDark: '#FFFFFF',        // White
    
    // Background Colors
    backgroundScreen: '#F8FAFC',  // Very Light Gray
    backgroundCard: '#FFFFFF',    // White
    
    // Border Colors
    border: '#E5E7EB',            // Light Gray
    
    // Status Colors
    success: '#10B981',
    warning: '#F59E0B',
    error: '#EF4444',
    info: '#3B82F6',
  },
  
  typography: {
    fontFamily: 'Poppins',
    
    heading1: {
      fontSize: 24,
      fontWeight: '600',
      lineHeight: 32,
    },
    
    largeDisplay: {
      fontSize: 32,
      fontWeight: '500',
      lineHeight: 40,
    },
    
    cardTitle: {
      fontSize: 16,
      fontWeight: '600',
      lineHeight: 24,
    },
    
    body: {
      fontSize: 14,
      fontWeight: '400',
      lineHeight: 20,
    },
    
    small: {
      fontSize: 12,
      fontWeight: '400',
      lineHeight: 16,
    },
    
    button: {
      fontSize: 14,
      fontWeight: '600',
      lineHeight: 20,
    },
  },
  
  spacing: {
    xs: 4,
    sm: 8,
    md: 12,
    lg: 16,
    xl: 20,
    xxl: 24,
    xxxl: 32,
  },
  
  borderRadius: {
    small: 8,
    medium: 12,
    large: 16,
    pill: 20,
  },
  
  shadows: {
    card: {
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 3, // for Android
    },
  },
};

// Helper functions for consistent styling
export const getSpacing = (size: keyof Theme['spacing']) => theme.spacing[size];
export const getColor = (color: keyof Theme['colors']) => theme.colors[color];
export const getBorderRadius = (size: keyof Theme['borderRadius']) => theme.borderRadius[size];
export const getTypography = (variant: keyof Theme['typography']) => theme.typography[variant];

// Status color mapping
export const getStatusColor = (status: string): string => {
  switch (status.toLowerCase()) {
    case 'scheduled':
      return theme.colors.info;
    case 'in-progress':
    case 'in_progress':
      return theme.colors.accent;
    case 'completed':
      return theme.colors.success;
    case 'missed':
    case 'failed':
    case 'cancelled':
      return theme.colors.error;
    default:
      return theme.colors.textSecondary;
  }
};

// Priority color mapping
export const getPriorityColor = (priority: string): string => {
  switch (priority.toLowerCase()) {
    case 'urgent':
      return theme.colors.error;
    case 'high':
      return theme.colors.accent;
    case 'medium':
      return theme.colors.warning;
    case 'low':
      return theme.colors.info;
    default:
      return theme.colors.textSecondary;
  }
};
