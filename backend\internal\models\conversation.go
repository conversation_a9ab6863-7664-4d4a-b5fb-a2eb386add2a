package models

import (
	"time"

	"gorm.io/gorm"
)

// Conversation represents a conversation between a user and an agent
type Conversation struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Conversation fields
	Title    string `json:"title" validate:"max=200"`
	IsActive bool   `json:"is_active" gorm:"default:true"`

	// Foreign keys
	UserID  uint `json:"user_id" gorm:"not null"`
	AgentID uint `json:"agent_id" gorm:"not null"`

	// Relationships
	User     User      `json:"user,omitempty" gorm:"foreignKey:UserID"`
	Agent    Agent     `json:"agent,omitempty" gorm:"foreignKey:AgentID"`
	Messages []Message `json:"messages,omitempty" gorm:"foreignKey:ConversationID"`
}

// ConversationCreateRequest represents the request payload for creating a conversation
type ConversationCreateRequest struct {
	Title   string `json:"title" validate:"max=200"`
	AgentID uint   `json:"agent_id" validate:"required"`
}

// ConversationUpdateRequest represents the request payload for updating a conversation
type ConversationUpdateRequest struct {
	Title    *string `json:"title,omitempty" validate:"omitempty,max=200"`
	IsActive *bool   `json:"is_active,omitempty"`
}

// ConversationResponse represents the response payload for conversation data
type ConversationResponse struct {
	ID        uint      `json:"id"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	Title     string    `json:"title"`
	IsActive  bool      `json:"is_active"`
	UserID    uint      `json:"user_id"`
	AgentID   uint      `json:"agent_id"`
}

// ConversationWithMessagesResponse includes messages in the response
type ConversationWithMessagesResponse struct {
	ConversationResponse
	Messages []MessageResponse `json:"messages"`
}

// ToResponse converts a Conversation model to ConversationResponse
func (c *Conversation) ToResponse() ConversationResponse {
	return ConversationResponse{
		ID:        c.ID,
		CreatedAt: c.CreatedAt,
		UpdatedAt: c.UpdatedAt,
		Title:     c.Title,
		IsActive:  c.IsActive,
		UserID:    c.UserID,
		AgentID:   c.AgentID,
	}
}

// ToResponseWithMessages converts a Conversation model to ConversationWithMessagesResponse
func (c *Conversation) ToResponseWithMessages() ConversationWithMessagesResponse {
	messages := make([]MessageResponse, len(c.Messages))
	for i, msg := range c.Messages {
		messages[i] = msg.ToResponse()
	}

	return ConversationWithMessagesResponse{
		ConversationResponse: c.ToResponse(),
		Messages:             messages,
	}
}
