# Development Guide

This guide provides detailed instructions for setting up and developing the AI Agent application.

## Prerequisites

- **Go 1.21+** for backend development
- **Node.js 18+** and npm for frontend development
- **Docker** and Docker Compose for containerization
- **Git** for version control

## Project Structure

```
├── backend/                 # Go backend application
│   ├── cmd/server/         # Application entry point
│   ├── internal/           # Private application code
│   │   ├── api/           # API handlers and routes
│   │   ├── middleware/    # HTTP middleware
│   │   ├── models/        # Data models
│   │   ├── services/      # Business logic
│   │   └── database/      # Database connection and migrations
│   ├── pkg/               # Public packages
│   └── tests/             # Backend tests
├── frontend/              # React + Expo application
│   ├── src/
│   │   ├── components/    # Reusable UI components
│   │   ├── screens/       # Application screens
│   │   ├── services/      # API services and state management
│   │   ├── types/         # TypeScript type definitions
│   │   └── utils/         # Utility functions
│   └── __tests__/         # Frontend tests
├── docs/                  # API documentation
├── docker/                # Docker configurations
└── .github/               # CI/CD workflows
```

## Backend Development

### Setup

1. Navigate to the backend directory:
   ```bash
   cd backend
   ```

2. Install dependencies:
   ```bash
   go mod tidy
   ```

3. Copy environment configuration:
   ```bash
   cp ../.env.example .env
   ```

4. Run the application:
   ```bash
   go run cmd/server/main.go
   ```

### Testing

Run all tests:
```bash
go test ./...
```

Run tests with coverage:
```bash
go test -cover ./...
```

Run specific test:
```bash
go test ./tests -run TestUserRegistration
```

### Database

The application uses SQLite with GORM for database operations. The database file is created automatically at `./data/ai_agent.db`.

#### Migrations

Migrations are handled automatically by GORM's AutoMigrate feature when the application starts.

### API Documentation

The API is documented using Swagger. After starting the server, visit:
- Swagger UI: http://localhost:8080/swagger/index.html

## Frontend Development

### Setup

1. Navigate to the frontend directory:
   ```bash
   cd frontend
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Start the development server:
   ```bash
   npm start
   ```

### Available Scripts

- `npm start` - Start Expo development server
- `npm run web` - Start web development server
- `npm test` - Run tests
- `npm run lint` - Run ESLint
- `npm run type-check` - Run TypeScript type checking

### Component Architecture

The frontend follows atomic design principles:

- **Atoms**: Basic building blocks (Button, Input, Text)
- **Molecules**: Simple combinations of atoms (SearchBox, FormField)
- **Organisms**: Complex UI components (Header, ProductList)
- **Templates**: Page-level layouts
- **Pages**: Specific instances of templates

### State Management

- **Zustand**: Global state management
- **React Query**: Server state and caching
- **React Context**: Theme and authentication context

## Docker Development

### Build and Run with Docker Compose

1. Build and start all services:
   ```bash
   docker-compose up --build
   ```

2. Run in background:
   ```bash
   docker-compose up -d --build
   ```

3. View logs:
   ```bash
   docker-compose logs -f
   ```

4. Stop services:
   ```bash
   docker-compose down
   ```

### Individual Service Development

Build backend only:
```bash
docker build -f docker/backend.Dockerfile -t ai-agent-backend .
```

Build frontend only:
```bash
docker build -f docker/frontend.Dockerfile -t ai-agent-frontend .
```

## Testing Strategy

### Backend Testing

- **Unit Tests**: Test individual functions and methods
- **Integration Tests**: Test API endpoints and database interactions
- **Table-Driven Tests**: Follow Go idioms for comprehensive test coverage

### Frontend Testing

- **Unit Tests**: Test individual components and utilities
- **Integration Tests**: Test component interactions
- **E2E Tests**: Test complete user workflows (future implementation)

## Code Quality

### Backend

- Use `golangci-lint` for linting
- Follow Go naming conventions
- Write comprehensive tests
- Use structured logging

### Frontend

- Use ESLint and Prettier for code formatting
- Follow TypeScript best practices
- Write unit tests for components
- Use proper TypeScript types

## Environment Configuration

### Development

Copy `.env.example` to `.env` and update values:

```bash
cp .env.example .env
```

### Production

Set environment variables in your deployment environment:

- `ENVIRONMENT=production`
- `JWT_SECRET=your-secure-secret`
- `DATABASE_URL=your-database-url`

## API Usage

### Authentication

1. Register a new user:
   ```bash
   curl -X POST http://localhost:8080/api/v1/auth/register \
     -H "Content-Type: application/json" \
     -d '{"email":"<EMAIL>","username":"testuser","password":"password123","first_name":"Test","last_name":"User"}'
   ```

2. Login:
   ```bash
   curl -X POST http://localhost:8080/api/v1/auth/login \
     -H "Content-Type: application/json" \
     -d '{"email":"<EMAIL>","password":"password123"}'
   ```

3. Use the returned token in subsequent requests:
   ```bash
   curl -H "Authorization: Bearer YOUR_TOKEN" http://localhost:8080/api/v1/users/me
   ```

## Troubleshooting

### Common Issues

1. **Port already in use**: Change the port in environment variables
2. **Database connection issues**: Ensure the data directory exists and has proper permissions
3. **CORS errors**: Update CORS_ORIGINS in environment configuration
4. **Module not found**: Run `go mod tidy` in backend or `npm install` in frontend

### Debugging

- Enable debug logging: Set `LOG_LEVEL=debug`
- Use Go debugger: Install and use `dlv`
- Use React Developer Tools for frontend debugging

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Run linting and tests
6. Submit a pull request

## Deployment

See the main README.md for deployment instructions using Docker Compose or individual containers.
