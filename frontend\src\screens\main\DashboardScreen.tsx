import React from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTheme } from '@/store/themeStore';
import { useAuth } from '@/store/authStore';
import { Text } from '@/components/atoms/Text';
import { Card } from '@/components/atoms/Card';

export const DashboardScreen: React.FC = () => {
  const theme = useTheme();
  const { user } = useAuth();

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.backgroundScreen }]}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <View style={styles.header}>
          <Text variant="heading1" color="textPrimary">
            Welcome {user?.first_name}!
          </Text>
          <Text variant="body" color="textSecondary" style={styles.subtitle}>
            Here's your AI Agent dashboard
          </Text>
        </View>

        <View style={styles.statsContainer}>
          <Card variant="summary" style={styles.statCard}>
            <Text variant="largeDisplay" color="accent" style={styles.statNumber}>
              7
            </Text>
            <Text variant="small" color="textSecondary">
              Active Agents
            </Text>
          </Card>

          <Card variant="summary" style={styles.statCard}>
            <Text variant="largeDisplay" color="primary" style={styles.statNumber}>
              12
            </Text>
            <Text variant="small" color="textSecondary">
              Conversations
            </Text>
          </Card>

          <Card variant="summary" style={styles.statCard}>
            <Text variant="largeDisplay" color="success" style={styles.statNumber}>
              5
            </Text>
            <Text variant="small" color="textSecondary">
              Completed Tasks
            </Text>
          </Card>
        </View>

        <Card style={styles.welcomeCard}>
          <Text variant="cardTitle" color="textPrimary" style={styles.cardTitle}>
            Getting Started
          </Text>
          <Text variant="body" color="textSecondary" style={styles.cardText}>
            Welcome to your AI Agent platform! You can create and manage AI agents, 
            have conversations, and track tasks all from this dashboard.
          </Text>
        </Card>

        <Card style={styles.featuresCard}>
          <Text variant="cardTitle" color="textPrimary" style={styles.cardTitle}>
            Key Features
          </Text>
          <View style={styles.featureList}>
            <Text variant="body" color="textSecondary" style={styles.featureItem}>
              • Create and customize AI agents
            </Text>
            <Text variant="body" color="textSecondary" style={styles.featureItem}>
              • Have intelligent conversations
            </Text>
            <Text variant="body" color="textSecondary" style={styles.featureItem}>
              • Manage tasks and workflows
            </Text>
            <Text variant="body" color="textSecondary" style={styles.featureItem}>
              • Track progress and analytics
            </Text>
          </View>
        </Card>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    padding: 20,
  },
  header: {
    marginBottom: 24,
  },
  subtitle: {
    marginTop: 8,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  statCard: {
    flex: 1,
    marginHorizontal: 4,
    alignItems: 'center',
    paddingVertical: 20,
  },
  statNumber: {
    textAlign: 'center',
    marginBottom: 4,
  },
  welcomeCard: {
    marginBottom: 16,
  },
  featuresCard: {
    marginBottom: 16,
  },
  cardTitle: {
    marginBottom: 12,
  },
  cardText: {
    lineHeight: 22,
  },
  featureList: {
    marginTop: 8,
  },
  featureItem: {
    marginBottom: 8,
    lineHeight: 20,
  },
});
