version: '3.8'

services:
  backend:
    build:
      context: .
      dockerfile: docker/backend.Dockerfile
    ports:
      - "8080:8080"
    environment:
      - ENVIRONMENT=production
      - PORT=8080
      - DATABASE_URL=/app/data/ai_agent.db
      - LOG_LEVEL=info
      - JWT_SECRET=your-secret-key-change-in-production
      - CORS_ORIGINS=http://localhost:3000,http://localhost:80
    volumes:
      - backend_data:/root/data
    networks:
      - ai-agent-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  frontend:
    build:
      context: .
      dockerfile: docker/frontend.Dockerfile
    ports:
      - "80:80"
    depends_on:
      - backend
    networks:
      - ai-agent-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:80"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  backend_data:
    driver: local

networks:
  ai-agent-network:
    driver: bridge
