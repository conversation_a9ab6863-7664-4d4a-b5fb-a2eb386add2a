package handlers

import (
	"net/http"
	"strconv"

	"github.com/bluehorn/ai-agent/internal/middleware"
	"github.com/bluehorn/ai-agent/internal/models"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// ConversationHandler handles conversation-related requests
type ConversationHandler struct {
	db     *gorm.DB
	logger *zap.Logger
}

// NewConversationHandler creates a new conversation handler
func NewConversationHandler(db *gorm.DB, logger *zap.Logger) *ConversationHandler {
	return &ConversationHandler{
		db:     db,
		logger: logger,
	}
}

// CreateConversation creates a new conversation
func (h *ConversationHandler) CreateConversation(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, middleware.ErrorResponse{
			Error: "User ID not found in context",
		})
		return
	}

	var req models.ConversationCreateRequest
	if !middleware.BindAndValidate(c, &req, h.logger) {
		return
	}

	conversation := models.Conversation{
		Title:    req.Title,
		UserID:   userID.(uint),
		AgentID:  req.AgentID,
		IsActive: true,
	}

	if err := h.db.Create(&conversation).Error; err != nil {
		h.logger.Error("Failed to create conversation", zap.Error(err))
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error: "Failed to create conversation",
		})
		return
	}

	h.logger.Info("Conversation created successfully", zap.Uint("conversation_id", conversation.ID))
	c.JSON(http.StatusCreated, conversation.ToResponse())
}

// GetConversations returns all conversations for the current user
func (h *ConversationHandler) GetConversations(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, middleware.ErrorResponse{
			Error: "User ID not found in context",
		})
		return
	}

	var conversations []models.Conversation
	if err := h.db.Where("user_id = ?", userID).Find(&conversations).Error; err != nil {
		h.logger.Error("Failed to fetch conversations", zap.Error(err))
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error: "Failed to fetch conversations",
		})
		return
	}

	responses := make([]models.ConversationResponse, len(conversations))
	for i, conversation := range conversations {
		responses[i] = conversation.ToResponse()
	}

	c.JSON(http.StatusOK, responses)
}

// GetConversation returns a specific conversation by ID
func (h *ConversationHandler) GetConversation(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, middleware.ErrorResponse{
			Error: "Invalid conversation ID",
		})
		return
	}

	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, middleware.ErrorResponse{
			Error: "User ID not found in context",
		})
		return
	}

	var conversation models.Conversation
	if err := h.db.Preload("Messages").Where("id = ? AND user_id = ?", uint(id), userID).First(&conversation).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, middleware.ErrorResponse{
				Error: "Conversation not found",
			})
			return
		}
		h.logger.Error("Failed to fetch conversation", zap.Error(err))
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error: "Failed to fetch conversation",
		})
		return
	}

	c.JSON(http.StatusOK, conversation.ToResponseWithMessages())
}

// UpdateConversation updates a specific conversation by ID
func (h *ConversationHandler) UpdateConversation(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, middleware.ErrorResponse{
			Error: "Invalid conversation ID",
		})
		return
	}

	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, middleware.ErrorResponse{
			Error: "User ID not found in context",
		})
		return
	}

	var req models.ConversationUpdateRequest
	if !middleware.BindAndValidate(c, &req, h.logger) {
		return
	}

	var conversation models.Conversation
	if err := h.db.Where("id = ? AND user_id = ?", uint(id), userID).First(&conversation).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, middleware.ErrorResponse{
				Error: "Conversation not found",
			})
			return
		}
		h.logger.Error("Failed to fetch conversation", zap.Error(err))
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error: "Failed to fetch conversation",
		})
		return
	}

	// Update fields if provided
	if req.Title != nil {
		conversation.Title = *req.Title
	}
	if req.IsActive != nil {
		conversation.IsActive = *req.IsActive
	}

	if err := h.db.Save(&conversation).Error; err != nil {
		h.logger.Error("Failed to update conversation", zap.Error(err))
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error: "Failed to update conversation",
		})
		return
	}

	h.logger.Info("Conversation updated successfully", zap.Uint("conversation_id", conversation.ID))
	c.JSON(http.StatusOK, conversation.ToResponse())
}

// DeleteConversation deletes a specific conversation by ID
func (h *ConversationHandler) DeleteConversation(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, middleware.ErrorResponse{
			Error: "Invalid conversation ID",
		})
		return
	}

	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, middleware.ErrorResponse{
			Error: "User ID not found in context",
		})
		return
	}

	if err := h.db.Where("id = ? AND user_id = ?", uint(id), userID).Delete(&models.Conversation{}).Error; err != nil {
		h.logger.Error("Failed to delete conversation", zap.Error(err))
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error: "Failed to delete conversation",
		})
		return
	}

	h.logger.Info("Conversation deleted successfully", zap.Uint("conversation_id", uint(id)))
	c.Status(http.StatusNoContent)
}
