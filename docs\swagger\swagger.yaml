openapi: 3.0.0
info:
  title: AI Agent API
  description: A comprehensive AI agent API built with Go and Gin framework
  version: 1.0.0
  contact:
    name: API Support
    url: http://www.swagger.io/support
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: http://localhost:8080/api/v1
    description: Development server
  - url: https://your-production-api.com/api/v1
    description: Production server

security:
  - BearerAuth: []

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT token for authentication

  schemas:
    User:
      type: object
      properties:
        id:
          type: integer
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        email:
          type: string
          format: email
        username:
          type: string
        first_name:
          type: string
        last_name:
          type: string
        role:
          type: string
          enum: [admin, user]
        is_active:
          type: boolean

    Agent:
      type: object
      properties:
        id:
          type: integer
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        name:
          type: string
        description:
          type: string
        model:
          type: string
        system_prompt:
          type: string
        temperature:
          type: number
          format: float
          minimum: 0
          maximum: 2
        max_tokens:
          type: integer
          minimum: 1
          maximum: 4000
        is_active:
          type: boolean
        user_id:
          type: integer

    Conversation:
      type: object
      properties:
        id:
          type: integer
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        title:
          type: string
        is_active:
          type: boolean
        user_id:
          type: integer
        agent_id:
          type: integer

    Message:
      type: object
      properties:
        id:
          type: integer
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        content:
          type: string
        role:
          type: string
          enum: [user, assistant, system]
        tokens:
          type: integer
        conversation_id:
          type: integer

    Task:
      type: object
      properties:
        id:
          type: integer
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        title:
          type: string
        description:
          type: string
        status:
          type: string
          enum: [pending, in_progress, completed, failed, cancelled]
        priority:
          type: string
          enum: [low, medium, high, urgent]
        due_date:
          type: string
          format: date-time
          nullable: true
        completed_at:
          type: string
          format: date-time
          nullable: true
        user_id:
          type: integer
        agent_id:
          type: integer
          nullable: true
        parent_id:
          type: integer
          nullable: true

    Error:
      type: object
      properties:
        error:
          type: string
        details:
          type: array
          items:
            type: object
            properties:
              field:
                type: string
              message:
                type: string

paths:
  /health:
    get:
      summary: Health check
      description: Check if the API is running
      tags:
        - Health
      security: []
      responses:
        '200':
          description: API is healthy
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  service:
                    type: string

  /auth/register:
    post:
      summary: Register a new user
      description: Create a new user account
      tags:
        - Authentication
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
                - username
                - password
                - first_name
                - last_name
              properties:
                email:
                  type: string
                  format: email
                username:
                  type: string
                  minLength: 3
                  maxLength: 50
                password:
                  type: string
                  minLength: 8
                first_name:
                  type: string
                  minLength: 1
                  maxLength: 100
                last_name:
                  type: string
                  minLength: 1
                  maxLength: 100
      responses:
        '201':
          description: User created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  token:
                    type: string
                  user:
                    $ref: '#/components/schemas/User'
        '400':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '409':
          description: User already exists
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /auth/login:
    post:
      summary: Login user
      description: Authenticate user and return JWT token
      tags:
        - Authentication
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
                - password
              properties:
                email:
                  type: string
                  format: email
                password:
                  type: string
      responses:
        '200':
          description: Login successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  token:
                    type: string
                  user:
                    $ref: '#/components/schemas/User'
        '400':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Invalid credentials
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /auth/refresh:
    post:
      summary: Refresh JWT token
      description: Refresh an existing JWT token
      tags:
        - Authentication
      responses:
        '200':
          description: Token refreshed successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  token:
                    type: string
                  user:
                    $ref: '#/components/schemas/User'
        '401':
          description: Invalid or expired token
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /users/me:
    get:
      summary: Get current user
      description: Get the current authenticated user's information
      tags:
        - Users
      responses:
        '200':
          description: User information
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /agents:
    get:
      summary: Get agents
      description: Get all agents for the current user
      tags:
        - Agents
      responses:
        '200':
          description: List of agents
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Agent'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

    post:
      summary: Create agent
      description: Create a new AI agent
      tags:
        - Agents
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - name
                - model
              properties:
                name:
                  type: string
                  minLength: 1
                  maxLength: 100
                description:
                  type: string
                  maxLength: 500
                model:
                  type: string
                system_prompt:
                  type: string
                  maxLength: 2000
                temperature:
                  type: number
                  format: float
                  minimum: 0
                  maximum: 2
                max_tokens:
                  type: integer
                  minimum: 1
                  maximum: 4000
      responses:
        '201':
          description: Agent created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Agent'
        '400':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

tags:
  - name: Health
    description: Health check endpoints
  - name: Authentication
    description: User authentication and authorization
  - name: Users
    description: User management
  - name: Agents
    description: AI agent management
  - name: Conversations
    description: Conversation management
  - name: Messages
    description: Message management
  - name: Tasks
    description: Task management
