import React, { createContext, useContext, useEffect } from 'react';
import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { User } from '@/types/api';

interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  
  // Actions
  setAuth: (user: User, token: string) => void;
  clearAuth: () => void;
  setLoading: (loading: boolean) => void;
  updateUser: (user: Partial<User>) => void;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: true,
      
      setAuth: (user: User, token: string) => {
        set({
          user,
          token,
          isAuthenticated: true,
          isLoading: false,
        });
      },
      
      clearAuth: () => {
        set({
          user: null,
          token: null,
          isAuthenticated: false,
          isLoading: false,
        });
      },
      
      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },
      
      updateUser: (userData: Partial<User>) => {
        const currentUser = get().user;
        if (currentUser) {
          set({
            user: { ...currentUser, ...userData },
          });
        }
      },
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);

// Auth Context for initialization
const AuthContext = createContext<{
  isInitialized: boolean;
}>({
  isInitialized: false,
});

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isInitialized, setIsInitialized] = React.useState(false);
  const setLoading = useAuthStore(state => state.setLoading);
  
  useEffect(() => {
    // Initialize auth state from storage
    const initializeAuth = async () => {
      try {
        // The persist middleware will automatically load the state
        setLoading(false);
        setIsInitialized(true);
      } catch (error) {
        console.error('Failed to initialize auth:', error);
        setLoading(false);
        setIsInitialized(true);
      }
    };
    
    initializeAuth();
  }, [setLoading]);
  
  return (
    <AuthContext.Provider value={{ isInitialized }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuthContext = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuthContext must be used within AuthProvider');
  }
  return context;
};

// Selectors for common use cases
export const useAuth = () => {
  const { user, token, isAuthenticated, isLoading } = useAuthStore();
  return { user, token, isAuthenticated, isLoading };
};

export const useAuthActions = () => {
  const { setAuth, clearAuth, setLoading, updateUser } = useAuthStore();
  return { setAuth, clearAuth, setLoading, updateUser };
};
