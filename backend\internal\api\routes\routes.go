package routes

import (
	"github.com/bluehorn/ai-agent/internal/api/handlers"
	"github.com/bluehorn/ai-agent/internal/middleware"
	"github.com/bluehorn/ai-agent/pkg/config"
	"github.com/bluehorn/ai-agent/pkg/logger"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"

	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
)

// SetupRoutes configures all the routes for the application
func SetupRoutes(router *gin.Engine, db *gorm.DB, zapLogger *zap.Logger) {
	cfg := config.Load()

	// Add middleware
	router.Use(logger.LoggerMiddleware(zapLogger))
	router.Use(gin.Recovery())
	router.Use(middleware.CORSMiddleware(cfg.CORSOrigins))
	router.Use(middleware.ValidateJSON(zapLogger))
	router.Use(middleware.RateLimitMiddleware(cfg.RateLimit, cfg.RateLimit*2))

	// Initialize handlers
	authHandler := handlers.NewAuthHandler(db, zapLogger, cfg.JWTSecret)
	userHandler := handlers.NewUserHandler(db, zapLogger)
	agentHandler := handlers.NewAgentHandler(db, zapLogger)
	conversationHandler := handlers.NewConversationHandler(db, zapLogger)
	messageHandler := handlers.NewMessageHandler(db, zapLogger)
	taskHandler := handlers.NewTaskHandler(db, zapLogger)

	// Health check endpoint
	router.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status":  "ok",
			"service": "ai-agent-api",
		})
	})

	// Swagger documentation
	router.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))

	// API v1 routes
	v1 := router.Group("/api/v1")
	{
		// Authentication routes (public)
		auth := v1.Group("/auth")
		{
			auth.POST("/register", authHandler.Register)
			auth.POST("/login", authHandler.Login)
			auth.POST("/refresh", authHandler.RefreshToken)
		}

		// Protected routes
		protected := v1.Group("/")
		protected.Use(middleware.AuthMiddleware(db, zapLogger, cfg.JWTSecret))
		{
			// User routes
			users := protected.Group("/users")
			{
				users.GET("/me", userHandler.GetCurrentUser)
				users.PUT("/me", userHandler.UpdateCurrentUser)
				users.DELETE("/me", userHandler.DeleteCurrentUser)
				
				// Admin only routes
				adminUsers := users.Group("/")
				adminUsers.Use(middleware.RequireRole("admin"))
				{
					adminUsers.GET("/", userHandler.GetUsers)
					adminUsers.GET("/:id", userHandler.GetUser)
					adminUsers.PUT("/:id", userHandler.UpdateUser)
					adminUsers.DELETE("/:id", userHandler.DeleteUser)
				}
			}

			// Agent routes
			agents := protected.Group("/agents")
			{
				agents.POST("/", agentHandler.CreateAgent)
				agents.GET("/", agentHandler.GetAgents)
				agents.GET("/:id", agentHandler.GetAgent)
				agents.PUT("/:id", agentHandler.UpdateAgent)
				agents.DELETE("/:id", agentHandler.DeleteAgent)
			}

			// Conversation routes
			conversations := protected.Group("/conversations")
			{
				conversations.POST("/", conversationHandler.CreateConversation)
				conversations.GET("/", conversationHandler.GetConversations)
				conversations.GET("/:id", conversationHandler.GetConversation)
				conversations.PUT("/:id", conversationHandler.UpdateConversation)
				conversations.DELETE("/:id", conversationHandler.DeleteConversation)

				// Message routes within conversations
				messages := conversations.Group("/:conversation_id/messages")
				{
					messages.POST("/", messageHandler.CreateMessage)
					messages.GET("/", messageHandler.GetMessages)
					messages.GET("/:id", messageHandler.GetMessage)
					messages.DELETE("/:id", messageHandler.DeleteMessage)
				}
			}

			// Task routes
			tasks := protected.Group("/tasks")
			{
				tasks.POST("/", taskHandler.CreateTask)
				tasks.GET("/", taskHandler.GetTasks)
				tasks.GET("/:id", taskHandler.GetTask)
				tasks.PUT("/:id", taskHandler.UpdateTask)
				tasks.DELETE("/:id", taskHandler.DeleteTask)
			}
		}
	}
}
