package handlers

import (
	"net/http"
	"strconv"

	"github.com/bluehorn/ai-agent/internal/middleware"
	"github.com/bluehorn/ai-agent/internal/models"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// TaskHandler handles task-related requests
type TaskHandler struct {
	db     *gorm.DB
	logger *zap.Logger
}

// NewTaskHandler creates a new task handler
func NewTaskHandler(db *gorm.DB, logger *zap.Logger) *TaskHandler {
	return &TaskHandler{
		db:     db,
		logger: logger,
	}
}

// CreateTask creates a new task
func (h *TaskHandler) CreateTask(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, middleware.ErrorResponse{
			Error: "User ID not found in context",
		})
		return
	}

	var req models.TaskCreateRequest
	if !middleware.BindAndValidate(c, &req, h.logger) {
		return
	}

	task := models.Task{
		Title:       req.Title,
		Description: req.Description,
		Priority:    req.Priority,
		DueDate:     req.DueDate,
		UserID:      userID.(uint),
		AgentID:     req.AgentID,
		ParentID:    req.ParentID,
		Status:      models.TaskStatusPending,
	}

	if err := h.db.Create(&task).Error; err != nil {
		h.logger.Error("Failed to create task", zap.Error(err))
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error: "Failed to create task",
		})
		return
	}

	h.logger.Info("Task created successfully", zap.Uint("task_id", task.ID))
	c.JSON(http.StatusCreated, task.ToResponse())
}

// GetTasks returns all tasks for the current user
func (h *TaskHandler) GetTasks(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, middleware.ErrorResponse{
			Error: "User ID not found in context",
		})
		return
	}

	var tasks []models.Task
	if err := h.db.Where("user_id = ?", userID).Find(&tasks).Error; err != nil {
		h.logger.Error("Failed to fetch tasks", zap.Error(err))
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error: "Failed to fetch tasks",
		})
		return
	}

	responses := make([]models.TaskResponse, len(tasks))
	for i, task := range tasks {
		responses[i] = task.ToResponse()
	}

	c.JSON(http.StatusOK, responses)
}

// GetTask returns a specific task by ID
func (h *TaskHandler) GetTask(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, middleware.ErrorResponse{
			Error: "Invalid task ID",
		})
		return
	}

	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, middleware.ErrorResponse{
			Error: "User ID not found in context",
		})
		return
	}

	var task models.Task
	if err := h.db.Where("id = ? AND user_id = ?", uint(id), userID).First(&task).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, middleware.ErrorResponse{
				Error: "Task not found",
			})
			return
		}
		h.logger.Error("Failed to fetch task", zap.Error(err))
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error: "Failed to fetch task",
		})
		return
	}

	c.JSON(http.StatusOK, task.ToResponse())
}

// UpdateTask updates a specific task by ID
func (h *TaskHandler) UpdateTask(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, middleware.ErrorResponse{
			Error: "Invalid task ID",
		})
		return
	}

	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, middleware.ErrorResponse{
			Error: "User ID not found in context",
		})
		return
	}

	var req models.TaskUpdateRequest
	if !middleware.BindAndValidate(c, &req, h.logger) {
		return
	}

	var task models.Task
	if err := h.db.Where("id = ? AND user_id = ?", uint(id), userID).First(&task).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, middleware.ErrorResponse{
				Error: "Task not found",
			})
			return
		}
		h.logger.Error("Failed to fetch task", zap.Error(err))
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error: "Failed to fetch task",
		})
		return
	}

	// Update fields if provided
	if req.Title != nil {
		task.Title = *req.Title
	}
	if req.Description != nil {
		task.Description = *req.Description
	}
	if req.Status != nil {
		task.Status = *req.Status
	}
	if req.Priority != nil {
		task.Priority = *req.Priority
	}
	if req.DueDate != nil {
		task.DueDate = req.DueDate
	}
	if req.AgentID != nil {
		task.AgentID = req.AgentID
	}

	if err := h.db.Save(&task).Error; err != nil {
		h.logger.Error("Failed to update task", zap.Error(err))
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error: "Failed to update task",
		})
		return
	}

	h.logger.Info("Task updated successfully", zap.Uint("task_id", task.ID))
	c.JSON(http.StatusOK, task.ToResponse())
}

// DeleteTask deletes a specific task by ID
func (h *TaskHandler) DeleteTask(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, middleware.ErrorResponse{
			Error: "Invalid task ID",
		})
		return
	}

	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, middleware.ErrorResponse{
			Error: "User ID not found in context",
		})
		return
	}

	if err := h.db.Where("id = ? AND user_id = ?", uint(id), userID).Delete(&models.Task{}).Error; err != nil {
		h.logger.Error("Failed to delete task", zap.Error(err))
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error: "Failed to delete task",
		})
		return
	}

	h.logger.Info("Task deleted successfully", zap.Uint("task_id", uint(id)))
	c.Status(http.StatusNoContent)
}
