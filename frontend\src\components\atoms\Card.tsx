import React from 'react';
import { View, StyleSheet, ViewStyle } from 'react-native';
import { useTheme } from '@/store/themeStore';
import { CardVariant } from '@/types/theme';

interface CardProps {
  children: React.ReactNode;
  variant?: CardVariant;
  style?: ViewStyle;
  padding?: keyof typeof theme.spacing;
}

export const Card: React.FC<CardProps> = ({
  children,
  variant = 'default',
  style,
  padding = 'lg',
}) => {
  const theme = useTheme();

  const getCardStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      borderRadius: theme.borderRadius.medium,
      ...theme.shadows.card,
    };

    switch (variant) {
      case 'status':
        baseStyle.backgroundColor = theme.colors.primary;
        break;
      case 'summary':
        baseStyle.backgroundColor = theme.colors.backgroundCard;
        baseStyle.borderWidth = 1;
        baseStyle.borderColor = theme.colors.border;
        break;
      default: // default
        baseStyle.backgroundColor = theme.colors.backgroundCard;
    }

    baseStyle.padding = theme.spacing[padding];

    return baseStyle;
  };

  return <View style={[getCardStyle(), style]}>{children}</View>;
};

const styles = StyleSheet.create({
  // Additional styles if needed
});
