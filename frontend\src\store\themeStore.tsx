import React, { createContext, useContext } from 'react';
import { create } from 'zustand';
import { theme as defaultTheme } from '@/utils/theme';
import { Theme } from '@/types/theme';

interface ThemeState {
  theme: Theme;
  isDarkMode: boolean;
  
  // Actions
  toggleDarkMode: () => void;
  setTheme: (theme: Theme) => void;
}

export const useThemeStore = create<ThemeState>((set, get) => ({
  theme: defaultTheme,
  isDarkMode: false,
  
  toggleDarkMode: () => {
    const { isDarkMode } = get();
    set({ isDarkMode: !isDarkMode });
    // Here you could also update the theme colors for dark mode
  },
  
  setTheme: (theme: Theme) => {
    set({ theme });
  },
}));

// Theme Context
const ThemeContext = createContext<Theme>(defaultTheme);

export const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const theme = useThemeStore(state => state.theme);
  
  return (
    <ThemeContext.Provider value={theme}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within ThemeProvider');
  }
  return context;
};

export const useThemeActions = () => {
  const { toggleDarkMode, setTheme } = useThemeStore();
  return { toggleDarkMode, setTheme };
};
