package handlers

import (
	"net/http"
	"strconv"

	"github.com/bluehorn/ai-agent/internal/middleware"
	"github.com/bluehorn/ai-agent/internal/models"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// AgentHandler handles agent-related requests
type Agent<PERSON><PERSON>ler struct {
	db     *gorm.DB
	logger *zap.Logger
}

// NewAgentHandler creates a new agent handler
func NewAgentHandler(db *gorm.DB, logger *zap.Logger) *AgentHandler {
	return &AgentHandler{
		db:     db,
		logger: logger,
	}
}

// CreateAgent creates a new agent
// @Summary Create agent
// @Description Create a new AI agent
// @Tags agents
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param agent body models.AgentCreateRequest true "Agent data"
// @Success 201 {object} models.AgentResponse
// @Failure 400 {object} middleware.ErrorResponse
// @Failure 401 {object} middleware.ErrorResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /agents [post]
func (h *AgentHandler) CreateAgent(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, middleware.ErrorResponse{
			Error: "User ID not found in context",
		})
		return
	}

	var req models.AgentCreateRequest
	if !middleware.BindAndValidate(c, &req, h.logger) {
		return
	}

	agent := models.Agent{
		Name:         req.Name,
		Description:  req.Description,
		Model:        req.Model,
		SystemPrompt: req.SystemPrompt,
		Temperature:  req.Temperature,
		MaxTokens:    req.MaxTokens,
		UserID:       userID.(uint),
		IsActive:     true,
	}

	if err := h.db.Create(&agent).Error; err != nil {
		h.logger.Error("Failed to create agent", zap.Error(err))
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error: "Failed to create agent",
		})
		return
	}

	h.logger.Info("Agent created successfully", zap.Uint("agent_id", agent.ID))
	c.JSON(http.StatusCreated, agent.ToResponse())
}

// GetAgents returns all agents for the current user
// @Summary Get agents
// @Description Get all agents for the current user
// @Tags agents
// @Security BearerAuth
// @Produce json
// @Success 200 {array} models.AgentResponse
// @Failure 401 {object} middleware.ErrorResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /agents [get]
func (h *AgentHandler) GetAgents(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, middleware.ErrorResponse{
			Error: "User ID not found in context",
		})
		return
	}

	var agents []models.Agent
	if err := h.db.Where("user_id = ?", userID).Find(&agents).Error; err != nil {
		h.logger.Error("Failed to fetch agents", zap.Error(err))
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error: "Failed to fetch agents",
		})
		return
	}

	responses := make([]models.AgentResponse, len(agents))
	for i, agent := range agents {
		responses[i] = agent.ToResponse()
	}

	c.JSON(http.StatusOK, responses)
}

// GetAgent returns a specific agent by ID
// @Summary Get agent by ID
// @Description Get a specific agent by ID
// @Tags agents
// @Security BearerAuth
// @Produce json
// @Param id path int true "Agent ID"
// @Success 200 {object} models.AgentResponse
// @Failure 400 {object} middleware.ErrorResponse
// @Failure 401 {object} middleware.ErrorResponse
// @Failure 404 {object} middleware.ErrorResponse
// @Router /agents/{id} [get]
func (h *AgentHandler) GetAgent(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, middleware.ErrorResponse{
			Error: "Invalid agent ID",
		})
		return
	}

	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, middleware.ErrorResponse{
			Error: "User ID not found in context",
		})
		return
	}

	var agent models.Agent
	if err := h.db.Where("id = ? AND user_id = ?", uint(id), userID).First(&agent).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, middleware.ErrorResponse{
				Error: "Agent not found",
			})
			return
		}
		h.logger.Error("Failed to fetch agent", zap.Error(err))
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error: "Failed to fetch agent",
		})
		return
	}

	c.JSON(http.StatusOK, agent.ToResponse())
}

// UpdateAgent updates a specific agent by ID
// @Summary Update agent by ID
// @Description Update a specific agent by ID
// @Tags agents
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "Agent ID"
// @Param agent body models.AgentUpdateRequest true "Agent update data"
// @Success 200 {object} models.AgentResponse
// @Failure 400 {object} middleware.ErrorResponse
// @Failure 401 {object} middleware.ErrorResponse
// @Failure 404 {object} middleware.ErrorResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /agents/{id} [put]
func (h *AgentHandler) UpdateAgent(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, middleware.ErrorResponse{
			Error: "Invalid agent ID",
		})
		return
	}

	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, middleware.ErrorResponse{
			Error: "User ID not found in context",
		})
		return
	}

	var req models.AgentUpdateRequest
	if !middleware.BindAndValidate(c, &req, h.logger) {
		return
	}

	var agent models.Agent
	if err := h.db.Where("id = ? AND user_id = ?", uint(id), userID).First(&agent).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, middleware.ErrorResponse{
				Error: "Agent not found",
			})
			return
		}
		h.logger.Error("Failed to fetch agent", zap.Error(err))
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error: "Failed to fetch agent",
		})
		return
	}

	// Update fields if provided
	if req.Name != nil {
		agent.Name = *req.Name
	}
	if req.Description != nil {
		agent.Description = *req.Description
	}
	if req.Model != nil {
		agent.Model = *req.Model
	}
	if req.SystemPrompt != nil {
		agent.SystemPrompt = *req.SystemPrompt
	}
	if req.Temperature != nil {
		agent.Temperature = *req.Temperature
	}
	if req.MaxTokens != nil {
		agent.MaxTokens = *req.MaxTokens
	}
	if req.IsActive != nil {
		agent.IsActive = *req.IsActive
	}

	if err := h.db.Save(&agent).Error; err != nil {
		h.logger.Error("Failed to update agent", zap.Error(err))
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error: "Failed to update agent",
		})
		return
	}

	h.logger.Info("Agent updated successfully", zap.Uint("agent_id", agent.ID))
	c.JSON(http.StatusOK, agent.ToResponse())
}

// DeleteAgent deletes a specific agent by ID
// @Summary Delete agent by ID
// @Description Delete a specific agent by ID
// @Tags agents
// @Security BearerAuth
// @Produce json
// @Param id path int true "Agent ID"
// @Success 204
// @Failure 400 {object} middleware.ErrorResponse
// @Failure 401 {object} middleware.ErrorResponse
// @Failure 404 {object} middleware.ErrorResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /agents/{id} [delete]
func (h *AgentHandler) DeleteAgent(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, middleware.ErrorResponse{
			Error: "Invalid agent ID",
		})
		return
	}

	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, middleware.ErrorResponse{
			Error: "User ID not found in context",
		})
		return
	}

	if err := h.db.Where("id = ? AND user_id = ?", uint(id), userID).Delete(&models.Agent{}).Error; err != nil {
		h.logger.Error("Failed to delete agent", zap.Error(err))
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error: "Failed to delete agent",
		})
		return
	}

	h.logger.Info("Agent deleted successfully", zap.Uint("agent_id", uint(id)))
	c.Status(http.StatusNoContent)
}
