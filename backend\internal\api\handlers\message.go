package handlers

import (
	"net/http"
	"strconv"

	"github.com/bluehorn/ai-agent/internal/middleware"
	"github.com/bluehorn/ai-agent/internal/models"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// MessageHandler handles message-related requests
type MessageHandler struct {
	db     *gorm.DB
	logger *zap.Logger
}

// NewMessageHandler creates a new message handler
func NewMessageHandler(db *gorm.DB, logger *zap.Logger) *MessageHandler {
	return &MessageHandler{
		db:     db,
		logger: logger,
	}
}

// CreateMessage creates a new message in a conversation
func (h *MessageHandler) CreateMessage(c *gin.Context) {
	conversationID, err := strconv.ParseUint(c.Param("conversation_id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, middleware.ErrorResponse{
			Error: "Invalid conversation ID",
		})
		return
	}

	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, middleware.ErrorResponse{
			Error: "User ID not found in context",
		})
		return
	}

	// Verify conversation belongs to user
	var conversation models.Conversation
	if err := h.db.Where("id = ? AND user_id = ?", uint(conversationID), userID).First(&conversation).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, middleware.ErrorResponse{
				Error: "Conversation not found",
			})
			return
		}
		h.logger.Error("Failed to fetch conversation", zap.Error(err))
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error: "Failed to fetch conversation",
		})
		return
	}

	var req models.MessageCreateRequest
	if !middleware.BindAndValidate(c, &req, h.logger) {
		return
	}

	message := models.Message{
		Content:        req.Content,
		Role:           req.Role,
		ConversationID: uint(conversationID),
	}

	if err := h.db.Create(&message).Error; err != nil {
		h.logger.Error("Failed to create message", zap.Error(err))
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error: "Failed to create message",
		})
		return
	}

	h.logger.Info("Message created successfully", zap.Uint("message_id", message.ID))
	c.JSON(http.StatusCreated, message.ToResponse())
}

// GetMessages returns all messages in a conversation
func (h *MessageHandler) GetMessages(c *gin.Context) {
	conversationID, err := strconv.ParseUint(c.Param("conversation_id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, middleware.ErrorResponse{
			Error: "Invalid conversation ID",
		})
		return
	}

	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, middleware.ErrorResponse{
			Error: "User ID not found in context",
		})
		return
	}

	// Verify conversation belongs to user
	var conversation models.Conversation
	if err := h.db.Where("id = ? AND user_id = ?", uint(conversationID), userID).First(&conversation).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, middleware.ErrorResponse{
				Error: "Conversation not found",
			})
			return
		}
		h.logger.Error("Failed to fetch conversation", zap.Error(err))
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error: "Failed to fetch conversation",
		})
		return
	}

	var messages []models.Message
	if err := h.db.Where("conversation_id = ?", conversationID).Order("created_at ASC").Find(&messages).Error; err != nil {
		h.logger.Error("Failed to fetch messages", zap.Error(err))
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error: "Failed to fetch messages",
		})
		return
	}

	responses := make([]models.MessageResponse, len(messages))
	for i, message := range messages {
		responses[i] = message.ToResponse()
	}

	c.JSON(http.StatusOK, responses)
}

// GetMessage returns a specific message by ID
func (h *MessageHandler) GetMessage(c *gin.Context) {
	conversationID, err := strconv.ParseUint(c.Param("conversation_id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, middleware.ErrorResponse{
			Error: "Invalid conversation ID",
		})
		return
	}

	messageID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, middleware.ErrorResponse{
			Error: "Invalid message ID",
		})
		return
	}

	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, middleware.ErrorResponse{
			Error: "User ID not found in context",
		})
		return
	}

	// Verify conversation belongs to user
	var conversation models.Conversation
	if err := h.db.Where("id = ? AND user_id = ?", uint(conversationID), userID).First(&conversation).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, middleware.ErrorResponse{
				Error: "Conversation not found",
			})
			return
		}
		h.logger.Error("Failed to fetch conversation", zap.Error(err))
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error: "Failed to fetch conversation",
		})
		return
	}

	var message models.Message
	if err := h.db.Where("id = ? AND conversation_id = ?", uint(messageID), conversationID).First(&message).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, middleware.ErrorResponse{
				Error: "Message not found",
			})
			return
		}
		h.logger.Error("Failed to fetch message", zap.Error(err))
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error: "Failed to fetch message",
		})
		return
	}

	c.JSON(http.StatusOK, message.ToResponse())
}

// DeleteMessage deletes a specific message by ID
func (h *MessageHandler) DeleteMessage(c *gin.Context) {
	conversationID, err := strconv.ParseUint(c.Param("conversation_id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, middleware.ErrorResponse{
			Error: "Invalid conversation ID",
		})
		return
	}

	messageID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, middleware.ErrorResponse{
			Error: "Invalid message ID",
		})
		return
	}

	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, middleware.ErrorResponse{
			Error: "User ID not found in context",
		})
		return
	}

	// Verify conversation belongs to user
	var conversation models.Conversation
	if err := h.db.Where("id = ? AND user_id = ?", uint(conversationID), userID).First(&conversation).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, middleware.ErrorResponse{
				Error: "Conversation not found",
			})
			return
		}
		h.logger.Error("Failed to fetch conversation", zap.Error(err))
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error: "Failed to fetch conversation",
		})
		return
	}

	if err := h.db.Where("id = ? AND conversation_id = ?", uint(messageID), conversationID).Delete(&models.Message{}).Error; err != nil {
		h.logger.Error("Failed to delete message", zap.Error(err))
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error: "Failed to delete message",
		})
		return
	}

	h.logger.Info("Message deleted successfully", zap.Uint("message_id", uint(messageID)))
	c.Status(http.StatusNoContent)
}
