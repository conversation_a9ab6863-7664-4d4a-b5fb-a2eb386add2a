import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { Button } from '@/components/atoms/Button';
import { ThemeProvider } from '@/store/themeStore';

// Mock the theme provider for testing
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <ThemeProvider>{children}</ThemeProvider>
);

describe('Button Component', () => {
  it('renders correctly with title', () => {
    const { getByText } = render(
      <TestWrapper>
        <Button title="Test Button" onPress={() => {}} />
      </TestWrapper>
    );
    
    expect(getByText('Test Button')).toBeTruthy();
  });

  it('calls onPress when pressed', () => {
    const mockOnPress = jest.fn();
    const { getByText } = render(
      <TestWrapper>
        <Button title="Test Button" onPress={mockOnPress} />
      </TestWrapper>
    );
    
    fireEvent.press(getByText('Test Button'));
    expect(mockOnPress).toHaveBeenCalledTimes(1);
  });

  it('shows loading indicator when loading', () => {
    const { getByTestId } = render(
      <TestWrapper>
        <Button title="Test Button" onPress={() => {}} loading />
      </TestWrapper>
    );
    
    expect(getByTestId('activity-indicator')).toBeTruthy();
  });

  it('is disabled when disabled prop is true', () => {
    const mockOnPress = jest.fn();
    const { getByText } = render(
      <TestWrapper>
        <Button title="Test Button" onPress={mockOnPress} disabled />
      </TestWrapper>
    );
    
    fireEvent.press(getByText('Test Button'));
    expect(mockOnPress).not.toHaveBeenCalled();
  });

  it('renders with different variants', () => {
    const { getByText: getSolidButton } = render(
      <TestWrapper>
        <Button title="Solid Button" onPress={() => {}} variant="solid" />
      </TestWrapper>
    );
    
    const { getByText: getOutlineButton } = render(
      <TestWrapper>
        <Button title="Outline Button" onPress={() => {}} variant="outline" />
      </TestWrapper>
    );
    
    expect(getSolidButton('Solid Button')).toBeTruthy();
    expect(getOutlineButton('Outline Button')).toBeTruthy();
  });
});
