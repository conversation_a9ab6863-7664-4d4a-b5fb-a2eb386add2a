package middleware

import (
	"net/http"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"golang.org/x/time/rate"
)

// RateLimiter holds the rate limiters for different clients
type RateLimiter struct {
	limiters map[string]*rate.Limiter
	mu       sync.RWMutex
	rate     rate.Limit
	burst    int
}

// NewRateLimiter creates a new rate limiter
func NewRateLimiter(rps int, burst int) *RateLimiter {
	return &RateLimiter{
		limiters: make(map[string]*rate.Limiter),
		rate:     rate.Limit(rps),
		burst:    burst,
	}
}

// getLimiter returns the rate limiter for a specific client
func (rl *RateLimiter) getLimiter(clientID string) *rate.Limiter {
	rl.mu.Lock()
	defer rl.mu.Unlock()

	limiter, exists := rl.limiters[clientID]
	if !exists {
		limiter = rate.NewLimiter(rl.rate, rl.burst)
		rl.limiters[clientID] = limiter
	}

	return limiter
}

// cleanupLimiters removes old limiters to prevent memory leaks
func (rl *RateLimiter) cleanupLimiters() {
	ticker := time.NewTicker(time.Minute)
	go func() {
		for range ticker.C {
			rl.mu.Lock()
			for clientID, limiter := range rl.limiters {
				// Remove limiters that haven't been used recently
				if limiter.Tokens() == float64(rl.burst) {
					delete(rl.limiters, clientID)
				}
			}
			rl.mu.Unlock()
		}
	}()
}

// RateLimitMiddleware creates a rate limiting middleware
func RateLimitMiddleware(rps int, burst int) gin.HandlerFunc {
	rateLimiter := NewRateLimiter(rps, burst)
	rateLimiter.cleanupLimiters()

	return func(c *gin.Context) {
		// Use client IP as identifier
		clientID := c.ClientIP()
		
		// For authenticated users, use user ID instead
		if userID, exists := c.Get("user_id"); exists {
			clientID = string(rune(userID.(uint)))
		}

		limiter := rateLimiter.getLimiter(clientID)

		if !limiter.Allow() {
			c.JSON(http.StatusTooManyRequests, gin.H{
				"error": "Rate limit exceeded",
				"retry_after": "60s",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}
