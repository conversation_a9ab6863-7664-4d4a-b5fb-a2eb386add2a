# Environment Configuration Example
# Copy this file to .env and update the values

# Application Environment
ENVIRONMENT=development
PORT=8080

# Database Configuration
DATABASE_URL=./data/ai_agent.db

# Logging
LOG_LEVEL=info

# JWT Configuration
JWT_SECRET=your-secret-key-change-in-production

# Rate Limiting
RATE_LIMIT=100

# CORS Configuration
CORS_ORIGINS=http://localhost:3000,http://localhost:19006

# Frontend Configuration (for development)
REACT_APP_API_URL=http://localhost:8080/api/v1
