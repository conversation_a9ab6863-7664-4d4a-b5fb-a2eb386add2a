package handlers

import (
	"net/http"

	"github.com/bluehorn/ai-agent/internal/middleware"
	"github.com/bluehorn/ai-agent/internal/models"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

// <PERSON>th<PERSON>andler handles authentication-related requests
type AuthHandler struct {
	db        *gorm.DB
	logger    *zap.Logger
	jwtSecret string
}

// NewAuthHandler creates a new auth handler
func NewAuthHandler(db *gorm.DB, logger *zap.Logger, jwtSecret string) *AuthHandler {
	return &AuthHandler{
		db:        db,
		logger:    logger,
		jwtSecret: jwtSecret,
	}
}

// LoginRequest represents the login request payload
type LoginRequest struct {
	Email    string `json:"email" validate:"required,email"`
	Password string `json:"password" validate:"required"`
}

// LoginResponse represents the login response payload
type LoginResponse struct {
	Token string                `json:"token"`
	User  models.UserResponse   `json:"user"`
}

// Register handles user registration
// @Summary Register a new user
// @Description Create a new user account
// @Tags auth
// @Accept json
// @Produce json
// @Param user body models.UserCreateRequest true "User registration data"
// @Success 201 {object} LoginResponse
// @Failure 400 {object} middleware.ErrorResponse
// @Failure 409 {object} middleware.ErrorResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /auth/register [post]
func (h *AuthHandler) Register(c *gin.Context) {
	var req models.UserCreateRequest
	if !middleware.BindAndValidate(c, &req, h.logger) {
		return
	}

	// Check if user already exists
	var existingUser models.User
	if err := h.db.Where("email = ? OR username = ?", req.Email, req.Username).First(&existingUser).Error; err == nil {
		h.logger.Warn("User already exists", zap.String("email", req.Email))
		c.JSON(http.StatusConflict, middleware.ErrorResponse{
			Error: "User with this email or username already exists",
		})
		return
	}

	// Hash password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		h.logger.Error("Failed to hash password", zap.Error(err))
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error: "Failed to process password",
		})
		return
	}

	// Create user
	user := models.User{
		Email:     req.Email,
		Username:  req.Username,
		Password:  string(hashedPassword),
		FirstName: req.FirstName,
		LastName:  req.LastName,
		Role:      "user",
		IsActive:  true,
	}

	if err := h.db.Create(&user).Error; err != nil {
		h.logger.Error("Failed to create user", zap.Error(err))
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error: "Failed to create user",
		})
		return
	}

	// Generate JWT token
	token, err := middleware.GenerateJWT(user, h.jwtSecret)
	if err != nil {
		h.logger.Error("Failed to generate token", zap.Error(err))
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error: "Failed to generate authentication token",
		})
		return
	}

	h.logger.Info("User registered successfully", zap.Uint("user_id", user.ID))
	c.JSON(http.StatusCreated, LoginResponse{
		Token: token,
		User:  user.ToResponse(),
	})
}

// Login handles user authentication
// @Summary Login user
// @Description Authenticate user and return JWT token
// @Tags auth
// @Accept json
// @Produce json
// @Param credentials body LoginRequest true "Login credentials"
// @Success 200 {object} LoginResponse
// @Failure 400 {object} middleware.ErrorResponse
// @Failure 401 {object} middleware.ErrorResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /auth/login [post]
func (h *AuthHandler) Login(c *gin.Context) {
	var req LoginRequest
	if !middleware.BindAndValidate(c, &req, h.logger) {
		return
	}

	// Find user by email
	var user models.User
	if err := h.db.Where("email = ?", req.Email).First(&user).Error; err != nil {
		h.logger.Warn("User not found", zap.String("email", req.Email))
		c.JSON(http.StatusUnauthorized, middleware.ErrorResponse{
			Error: "Invalid email or password",
		})
		return
	}

	// Check if user is active
	if !user.IsActive {
		h.logger.Warn("Inactive user login attempt", zap.Uint("user_id", user.ID))
		c.JSON(http.StatusUnauthorized, middleware.ErrorResponse{
			Error: "Account is inactive",
		})
		return
	}

	// Verify password
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(req.Password)); err != nil {
		h.logger.Warn("Invalid password", zap.Uint("user_id", user.ID))
		c.JSON(http.StatusUnauthorized, middleware.ErrorResponse{
			Error: "Invalid email or password",
		})
		return
	}

	// Generate JWT token
	token, err := middleware.GenerateJWT(user, h.jwtSecret)
	if err != nil {
		h.logger.Error("Failed to generate token", zap.Error(err))
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error: "Failed to generate authentication token",
		})
		return
	}

	h.logger.Info("User logged in successfully", zap.Uint("user_id", user.ID))
	c.JSON(http.StatusOK, LoginResponse{
		Token: token,
		User:  user.ToResponse(),
	})
}

// RefreshToken handles token refresh
// @Summary Refresh JWT token
// @Description Refresh an existing JWT token
// @Tags auth
// @Security BearerAuth
// @Produce json
// @Success 200 {object} LoginResponse
// @Failure 401 {object} middleware.ErrorResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /auth/refresh [post]
func (h *AuthHandler) RefreshToken(c *gin.Context) {
	// Get user from context (set by auth middleware)
	userInterface, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusUnauthorized, middleware.ErrorResponse{
			Error: "User not found in context",
		})
		return
	}

	user := userInterface.(models.User)

	// Generate new JWT token
	token, err := middleware.GenerateJWT(user, h.jwtSecret)
	if err != nil {
		h.logger.Error("Failed to generate token", zap.Error(err))
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error: "Failed to generate authentication token",
		})
		return
	}

	h.logger.Info("Token refreshed successfully", zap.Uint("user_id", user.ID))
	c.JSON(http.StatusOK, LoginResponse{
		Token: token,
		User:  user.ToResponse(),
	})
}
