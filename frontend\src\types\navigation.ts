import { NavigatorScreenParams } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RouteProp } from '@react-navigation/native';

// Root Stack Navigator
export type RootStackParamList = {
  Auth: NavigatorScreenParams<AuthStackParamList>;
  Main: NavigatorScreenParams<MainTabParamList>;
};

// Auth Stack Navigator
export type AuthStackParamList = {
  Login: undefined;
  Register: undefined;
  ForgotPassword: undefined;
};

// Main Tab Navigator
export type MainTabParamList = {
  Dashboard: undefined;
  Agents: NavigatorScreenParams<AgentStackParamList>;
  Conversations: NavigatorScreenParams<ConversationStackParamList>;
  Tasks: NavigatorScreenParams<TaskStackParamList>;
  Profile: undefined;
};

// Agent Stack Navigator
export type AgentStackParamList = {
  AgentList: undefined;
  AgentDetail: { agentId: number };
  AgentCreate: undefined;
  AgentEdit: { agentId: number };
};

// Conversation Stack Navigator
export type ConversationStackParamList = {
  ConversationList: undefined;
  ConversationDetail: { conversationId: number };
  ConversationCreate: undefined;
};

// Task Stack Navigator
export type TaskStackParamList = {
  TaskList: undefined;
  TaskDetail: { taskId: number };
  TaskCreate: undefined;
  TaskEdit: { taskId: number };
};

// Navigation Props
export type RootStackNavigationProp = StackNavigationProp<RootStackParamList>;
export type AuthStackNavigationProp = StackNavigationProp<AuthStackParamList>;
export type MainTabNavigationProp = StackNavigationProp<MainTabParamList>;
export type AgentStackNavigationProp = StackNavigationProp<AgentStackParamList>;
export type ConversationStackNavigationProp = StackNavigationProp<ConversationStackParamList>;
export type TaskStackNavigationProp = StackNavigationProp<TaskStackParamList>;

// Route Props
export type AuthStackRouteProp<T extends keyof AuthStackParamList> = RouteProp<AuthStackParamList, T>;
export type MainTabRouteProp<T extends keyof MainTabParamList> = RouteProp<MainTabParamList, T>;
export type AgentStackRouteProp<T extends keyof AgentStackParamList> = RouteProp<AgentStackParamList, T>;
export type ConversationStackRouteProp<T extends keyof ConversationStackParamList> = RouteProp<ConversationStackParamList, T>;
export type TaskStackRouteProp<T extends keyof TaskStackParamList> = RouteProp<TaskStackParamList, T>;
