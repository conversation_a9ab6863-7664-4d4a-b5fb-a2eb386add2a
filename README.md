# AI Agent Application

A comprehensive full-stack AI agent platform built with Go backend and React + Expo frontend, featuring modern architecture, authentication, and real-time capabilities.

## 🚀 Features

- **User Management**: Registration, authentication, and profile management
- **AI Agent Creation**: Create and customize AI agents with different models and parameters
- **Conversations**: Real-time chat interface with AI agents
- **Task Management**: Create, track, and manage tasks with AI assistance
- **Modern UI**: Responsive design with atomic component architecture
- **Security**: JWT authentication, rate limiting, and input validation
- **API Documentation**: Comprehensive Swagger documentation
- **Testing**: Unit and integration tests for both frontend and backend
- **Containerization**: Docker support for easy deployment

## 🏗️ Architecture

### Backend (Go + Gin)
- **Framework**: Gin HTTP framework
- **Database**: SQLite with GORM ORM
- **Authentication**: JWT tokens with middleware
- **Logging**: Structured logging with Zap
- **Validation**: Request validation and sanitization
- **Security**: Rate limiting, CORS, and security headers

### Frontend (React + Expo + TypeScript)
- **Framework**: React with Expo for cross-platform support
- **Language**: TypeScript for type safety
- **State Management**: Zustand for global state, React Query for server state
- **Navigation**: React Navigation with type-safe routing
- **UI Components**: Atomic design pattern with custom theme system
- **Testing**: Jest with React Testing Library

## 📋 Prerequisites

- **Go 1.21+**
- **Node.js 18+**
- **Docker & Docker Compose** (optional, for containerized deployment)

## 🚀 Quick Start

### Using Docker Compose (Recommended)

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd ai-agent-app
   ```

2. Copy environment configuration:
   ```bash
   cp .env.example .env
   ```

3. Start the application:
   ```bash
   docker-compose up --build
   ```

4. Access the application:
   - Frontend: http://localhost:80
   - Backend API: http://localhost:8080
   - API Documentation: http://localhost:8080/swagger/index.html

### Manual Setup

#### Backend Setup

1. Navigate to backend directory:
   ```bash
   cd backend
   ```

2. Install dependencies:
   ```bash
   go mod tidy
   ```

3. Run the server:
   ```bash
   go run cmd/server/main.go
   ```

#### Frontend Setup

1. Navigate to frontend directory:
   ```bash
   cd frontend
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Start the development server:
   ```bash
   npm start
   ```

## 📚 API Documentation

The API is fully documented using Swagger/OpenAPI 3.0. After starting the backend server, visit:

- **Swagger UI**: http://localhost:8080/swagger/index.html
- **OpenAPI Spec**: `docs/swagger/swagger.yaml`
- **Postman Collection**: `docs/postman/AI_Agent_API.postman_collection.json`

### Key Endpoints

- `POST /api/v1/auth/register` - User registration
- `POST /api/v1/auth/login` - User login
- `GET /api/v1/users/me` - Get current user
- `GET /api/v1/agents` - List AI agents
- `POST /api/v1/agents` - Create AI agent
- `GET /api/v1/conversations` - List conversations
- `POST /api/v1/conversations` - Create conversation
- `GET /api/v1/tasks` - List tasks
- `POST /api/v1/tasks` - Create task

## 🧪 Testing

### Backend Tests

```bash
cd backend
go test ./...
```

### Frontend Tests

```bash
cd frontend
npm test
```

### Integration Tests

```bash
# Run all tests
docker-compose -f docker-compose.test.yml up --build --abort-on-container-exit
```

## 🔧 Configuration

### Environment Variables

Copy `.env.example` to `.env` and configure:

```env
# Application
ENVIRONMENT=development
PORT=8080

# Database
DATABASE_URL=./data/ai_agent.db

# Security
JWT_SECRET=your-secret-key
RATE_LIMIT=100

# CORS
CORS_ORIGINS=http://localhost:3000,http://localhost:19006
```

### Frontend Configuration

Update `frontend/src/config/api.ts` for API endpoint configuration.

## 📱 Mobile Development

The frontend is built with Expo, supporting both web and mobile platforms:

### Web Development
```bash
cd frontend
npm run web
```

### Mobile Development
```bash
cd frontend
npm start
# Scan QR code with Expo Go app
```

### Building for Production
```bash
cd frontend
npm run build  # Web build
expo build:android  # Android build
expo build:ios  # iOS build
```

## 🐳 Docker Deployment

### Production Deployment

1. Build and deploy:
   ```bash
   docker-compose -f docker-compose.prod.yml up -d --build
   ```

2. Scale services:
   ```bash
   docker-compose -f docker-compose.prod.yml up -d --scale backend=3
   ```

### Individual Services

Build backend:
```bash
docker build -f docker/backend.Dockerfile -t ai-agent-backend .
```

Build frontend:
```bash
docker build -f docker/frontend.Dockerfile -t ai-agent-frontend .
```

## 🔒 Security Features

- **JWT Authentication**: Secure token-based authentication
- **Rate Limiting**: Prevent API abuse
- **Input Validation**: Comprehensive request validation
- **CORS Protection**: Configurable cross-origin resource sharing
- **Security Headers**: Standard security headers implementation
- **Password Hashing**: Bcrypt password hashing
- **SQL Injection Prevention**: GORM ORM with prepared statements

## 📊 Monitoring & Logging

- **Structured Logging**: JSON-formatted logs with Zap
- **Health Checks**: Built-in health check endpoints
- **Request Logging**: Comprehensive request/response logging
- **Error Tracking**: Detailed error logging and tracking

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit your changes: `git commit -m 'Add amazing feature'`
4. Push to the branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

### Development Guidelines

- Follow the existing code style
- Write tests for new features
- Update documentation as needed
- Ensure all tests pass before submitting PR

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:

- Create an issue in the repository
- Check the [Development Guide](DEVELOPMENT.md) for detailed setup instructions
- Review the API documentation for endpoint details

## 🗺️ Roadmap

- [ ] Real-time messaging with WebSockets
- [ ] File upload and processing capabilities
- [ ] Advanced AI model integrations
- [ ] Analytics and reporting dashboard
- [ ] Multi-tenant support
- [ ] Advanced task automation
- [ ] Plugin system for extensibility

## 🙏 Acknowledgments

- [Gin](https://gin-gonic.com/) - HTTP web framework for Go
- [React](https://reactjs.org/) - JavaScript library for building user interfaces
- [Expo](https://expo.dev/) - Platform for universal React applications
- [GORM](https://gorm.io/) - ORM library for Golang
- [Zustand](https://github.com/pmndrs/zustand) - State management for React
